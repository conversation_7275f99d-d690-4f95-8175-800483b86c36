/*
 * SHT30 Temperature Sensor Pin Scanner
 *
 * This program automatically detects the correct SDA and SCL pins for the SHT30
 * temperature sensor by testing all possible I2C pin combinations and checking
 * for valid sensor responses.
 *
 * The SHT30 sensor uses I2C communication with default address 0x44 or 0x45.
 * This scanner will try different pin combinations until it finds a working one.
 *
 * Usage:
 * 1. Connect your SHT30 sensor to your device (but don't worry about pin order)
 * 2. Upload this code to your device
 * 3. Open Serial Monitor at 115200 baud
 * 4. Wait for the scan to complete
 * 5. Note the working SDA/SCL pin combination
 *
 * Author: SHT30 Pin Scanner Generator
 * Date: 2025-07-27
 */

#include <Arduino.h>
#include <Wire.h>

// Include your device configuration for platform detection
#include "../../UniversalSwitchControl/DeviceConfig.h"

// SHT30 I2C addresses (try both common addresses)
#define SHT30_ADDRESS_1 0x44
#define SHT30_ADDRESS_2 0x45

// SHT30 commands
#define SHT30_CMD_MEASURE_HIGH_REP 0x2C06 // High repeatability measurement
#define SHT30_CMD_SOFT_RESET 0x30A2       // Soft reset command

// Test configuration
#define SERIAL_BAUD_RATE 115200
#define I2C_TIMEOUT_MS 1000
#define MEASUREMENT_DELAY_MS 500

// Possible I2C pins for different platforms
#ifdef IS_ESP32
// ESP32 I2C capable pins (most GPIO pins can be used for I2C)
const uint8_t POSSIBLE_I2C_PINS[] = {
    0, 2, 4, 5, 12, 13, 14, 15, 16, 17, 18, 19, 21, 22, 23, 25, 26, 27, 32, 33};
const char *PLATFORM_NAME = "ESP32";
#else
// ESP8266 I2C capable pins
const uint8_t POSSIBLE_I2C_PINS[] = {
    0, 1, 2, 3, 4, 5, 12, 13, 14, 15, 16};
const char *PLATFORM_NAME = "ESP8266";
#endif

const uint8_t NUM_I2C_PINS = sizeof(POSSIBLE_I2C_PINS) / sizeof(POSSIBLE_I2C_PINS[0]);

// Results storage
struct I2CResult
{
  uint8_t sda_pin;
  uint8_t scl_pin;
  uint8_t address;
  bool success;
  float temperature;
  float humidity;
};

I2CResult workingConfigurations[10]; // Store up to 10 working configurations
uint8_t workingConfigCount = 0;

void setup()
{
  Serial.begin(SERIAL_BAUD_RATE);
  delay(2000); // Wait for serial monitor

  Serial.println("========================================");
  Serial.println("SHT30 Temperature Sensor Pin Scanner");
  Serial.println("========================================");
  Serial.print("Platform: ");
  Serial.println(PLATFORM_NAME);
  Serial.print("Testing ");
  Serial.print(NUM_I2C_PINS * (NUM_I2C_PINS - 1));
  Serial.println(" pin combinations...");
  Serial.println("This may take several minutes...");
  Serial.println("========================================\n");

  // Print current device configuration for reference
  printCurrentConfiguration();

  Serial.println("Starting I2C pin scan...\n");

  // Test all possible SDA/SCL combinations
  scanAllI2CPinCombinations();

  // Print results
  printResults();
}

void loop()
{
  // Test the working configurations continuously
  if (workingConfigCount > 0)
  {
    Serial.println("\n--- Continuous Testing of Working Configurations ---");

    for (uint8_t i = 0; i < workingConfigCount; i++)
    {
      I2CResult &config = workingConfigurations[i];

      Serial.print("Testing SDA=");
      Serial.print(config.sda_pin);
      Serial.print(", SCL=");
      Serial.print(config.scl_pin);
      Serial.print(" (0x");
      Serial.print(config.address, HEX);
      Serial.print("): ");

      if (testSHT30Configuration(config.sda_pin, config.scl_pin, config.address))
      {
        float temp, hum;
        if (readSHT30Data(temp, hum))
        {
          Serial.print("T=");
          Serial.print(temp, 1);
          Serial.print("°C, H=");
          Serial.print(hum, 1);
          Serial.println("%");
        }
        else
        {
          Serial.println("Read failed");
        }
      }
      else
      {
        Serial.println("Connection failed");
      }
    }

    delay(5000); // Wait 5 seconds before next test cycle
  }
  else
  {
    Serial.println("No working configurations found. Check connections and restart.");
    delay(10000);
  }
}

void printCurrentConfiguration()
{
  Serial.println("Current Device Configuration:");
#ifdef DEVICE_MODEL_COOLER_CONTROL
  Serial.print("- Configured SDA Pin: GPIO");
  Serial.println(TEMP_SENSOR_SDA_PIN);
  Serial.print("- Configured SCL Pin: GPIO");
  Serial.println(TEMP_SENSOR_SCL_PIN);
#else
  Serial.println("- No temperature sensor configured for this device model");
#endif
  Serial.println();
}

void scanAllI2CPinCombinations()
{
  uint16_t totalCombinations = NUM_I2C_PINS * (NUM_I2C_PINS - 1);
  uint16_t currentCombination = 0;

  for (uint8_t sda_idx = 0; sda_idx < NUM_I2C_PINS; sda_idx++)
  {
    for (uint8_t scl_idx = 0; scl_idx < NUM_I2C_PINS; scl_idx++)
    {
      if (sda_idx == scl_idx)
        continue; // Skip same pin for SDA and SCL

      uint8_t sda_pin = POSSIBLE_I2C_PINS[sda_idx];
      uint8_t scl_pin = POSSIBLE_I2C_PINS[scl_idx];

      currentCombination++;

      // Print progress every 20 combinations
      if (currentCombination % 20 == 0 || currentCombination == totalCombinations)
      {
        Serial.print("Progress: ");
        Serial.print(currentCombination);
        Serial.print("/");
        Serial.print(totalCombinations);
        Serial.print(" (");
        Serial.print((currentCombination * 100) / totalCombinations);
        Serial.println("%)");
      }

      // Test both possible SHT30 addresses
      if (testSHT30Configuration(sda_pin, scl_pin, SHT30_ADDRESS_1))
      {
        recordWorkingConfiguration(sda_pin, scl_pin, SHT30_ADDRESS_1);
      }

      if (testSHT30Configuration(sda_pin, scl_pin, SHT30_ADDRESS_2))
      {
        recordWorkingConfiguration(sda_pin, scl_pin, SHT30_ADDRESS_2);
      }
    }
  }
}

bool testSHT30Configuration(uint8_t sda_pin, uint8_t scl_pin, uint8_t address)
{
  // Initialize I2C with the specified pins
  Wire.end(); // End any previous I2C session
  delay(10);

#ifdef IS_ESP32
  Wire.begin(sda_pin, scl_pin);
#else
  Wire.begin(sda_pin, scl_pin);
#endif

  Wire.setClock(100000); // 100kHz I2C clock
  delay(50);

  // Try to communicate with the sensor
  Wire.beginTransmission(address);
  Wire.write(0x30); // Soft reset command high byte
  Wire.write(0xA2); // Soft reset command low byte
  uint8_t error = Wire.endTransmission();

  if (error != 0)
  {
    return false; // Communication failed
  }

  delay(100); // Wait for reset

  // Try to start a measurement
  Wire.beginTransmission(address);
  Wire.write(0x2C); // High repeatability measurement high byte
  Wire.write(0x06); // High repeatability measurement low byte
  error = Wire.endTransmission();

  if (error != 0)
  {
    return false; // Measurement command failed
  }

  delay(20); // Wait for measurement

  // Try to read the result
  Wire.requestFrom(address, (uint8_t)6);
  if (Wire.available() != 6)
  {
    return false; // Didn't receive expected data
  }

  // Read and validate the data
  uint8_t data[6];
  for (int i = 0; i < 6; i++)
  {
    data[i] = Wire.read();
  }

  // Basic validation - check if we got reasonable temperature/humidity values
  uint16_t temp_raw = (data[0] << 8) | data[1];
  uint16_t hum_raw = (data[3] << 8) | data[4];

  float temperature = -45.0 + 175.0 * temp_raw / 65535.0;
  float humidity = 100.0 * hum_raw / 65535.0;

  // Validate reasonable ranges
  if (temperature >= -40.0 && temperature <= 85.0 &&
      humidity >= 0.0 && humidity <= 100.0)
  {
    return true;
  }

  return false;
}

void recordWorkingConfiguration(uint8_t sda_pin, uint8_t scl_pin, uint8_t address)
{
  if (workingConfigCount >= 10)
    return; // Array full

  // Check if this configuration is already recorded
  for (uint8_t i = 0; i < workingConfigCount; i++)
  {
    if (workingConfigurations[i].sda_pin == sda_pin &&
        workingConfigurations[i].scl_pin == scl_pin &&
        workingConfigurations[i].address == address)
    {
      return; // Already recorded
    }
  }

  // Record the new working configuration
  I2CResult &config = workingConfigurations[workingConfigCount];
  config.sda_pin = sda_pin;
  config.scl_pin = scl_pin;
  config.address = address;
  config.success = true;

  // Try to read current values
  if (readSHT30Data(config.temperature, config.humidity))
  {
    Serial.print("*** FOUND WORKING CONFIGURATION *** SDA=GPIO");
    Serial.print(sda_pin);
    Serial.print(", SCL=GPIO");
    Serial.print(scl_pin);
    Serial.print(", Address=0x");
    Serial.print(address, HEX);
    Serial.print(" - T=");
    Serial.print(config.temperature, 1);
    Serial.print("°C, H=");
    Serial.print(config.humidity, 1);
    Serial.println("%");
  }

  workingConfigCount++;
}

bool readSHT30Data(float &temperature, float &humidity)
{
  // Start a new measurement first
  Wire.beginTransmission(workingConfigurations[0].address);
  Wire.write(0x2C); // High repeatability measurement high byte
  Wire.write(0x06); // High repeatability measurement low byte
  uint8_t error = Wire.endTransmission();

  if (error != 0)
  {
    return false;
  }

  delay(20); // Wait for measurement to complete

  // Request 6 bytes from sensor
  Wire.requestFrom(workingConfigurations[0].address, (uint8_t)6);

  if (Wire.available() != 6)
  {
    return false;
  }

  uint8_t data[6];
  for (int i = 0; i < 6; i++)
  {
    data[i] = Wire.read();
  }

  // Convert raw data to temperature and humidity
  uint16_t temp_raw = (data[0] << 8) | data[1];
  uint16_t hum_raw = (data[3] << 8) | data[4];

  temperature = -45.0 + 175.0 * temp_raw / 65535.0;
  humidity = 100.0 * hum_raw / 65535.0;

  // Validate reasonable ranges
  if (temperature >= -40.0 && temperature <= 85.0 &&
      humidity >= 0.0 && humidity <= 100.0)
  {
    return true;
  }

  return false;
}

void printResults()
{
  Serial.println("\n========================================");
  Serial.println("SCAN RESULTS");
  Serial.println("========================================");

  if (workingConfigCount == 0)
  {
    Serial.println("❌ No working SHT30 configurations found!");
    Serial.println("\nPossible issues:");
    Serial.println("- SHT30 sensor not connected");
    Serial.println("- Wrong power supply (3.3V required)");
    Serial.println("- Faulty sensor or connections");
    Serial.println("- Sensor address different from 0x44/0x45");
  }
  else
  {
    Serial.print("✅ Found ");
    Serial.print(workingConfigCount);
    Serial.println(" working configuration(s):");
    Serial.println();

    for (uint8_t i = 0; i < workingConfigCount; i++)
    {
      I2CResult &config = workingConfigurations[i];
      Serial.print("Configuration ");
      Serial.print(i + 1);
      Serial.println(":");
      Serial.print("  SDA Pin: GPIO");
      Serial.println(config.sda_pin);
      Serial.print("  SCL Pin: GPIO");
      Serial.println(config.scl_pin);
      Serial.print("  I2C Address: 0x");
      Serial.println(config.address, HEX);
      Serial.print("  Temperature: ");
      Serial.print(config.temperature, 1);
      Serial.println("°C");
      Serial.print("  Humidity: ");
      Serial.print(config.humidity, 1);
      Serial.println("%");
      Serial.println();
    }

    Serial.println("RECOMMENDED CONFIGURATION:");
    I2CResult &best = workingConfigurations[0];
    Serial.print("Use SDA=GPIO");
    Serial.print(best.sda_pin);
    Serial.print(" and SCL=GPIO");
    Serial.print(best.scl_pin);
    Serial.print(" with address 0x");
    Serial.println(best.address, HEX);
  }

  Serial.println("========================================");
}

/*
 * Simple SHT30 Test with Adafruit Library - Pins 21,22
 * 
 * This test uses the Adafruit SHT31 library to test your SHT30 sensor
 * on the known pins SDA=21, SCL=22.
 * 
 * Required Library:
 * Install "Adafruit SHT31" library from Arduino Library Manager
 * (Also installs Adafruit Sensor library automatically)
 * 
 * Since your PCB is engineered and tested, this should work if the
 * ADDR pin is properly connected to set the I2C address.
 * 
 * Author: SHT30 Adafruit Simple Test
 * Date: 2025-01-27
 */

#include <Wire.h>
#include "Adafruit_SHT31.h"

// Pin configuration - you confirmed these are correct
#define SDA_PIN 21
#define SCL_PIN 22

// Create sensor objects for both possible addresses
Adafruit_SHT31 sht30_44 = Adafruit_SHT31();
Adafruit_SHT31 sht30_45 = Adafruit_SHT31();

// Track which sensor is working
bool sensor_44_working = false;
bool sensor_45_working = false;
uint32_t successCount = 0;
uint32_t errorCount = 0;

void setup() {
  Serial.begin(115200);
  delay(2000);
  
  Serial.println("========================================");
  Serial.println("  SHT30 ADAFRUIT LIBRARY TEST");
  Serial.println("========================================");
  Serial.print("SDA Pin: GPIO");
  Serial.println(SDA_PIN);
  Serial.print("SCL Pin: GPIO");
  Serial.println(SCL_PIN);
  Serial.println("Using Adafruit SHT31 library");
  Serial.println("========================================\n");
  
  // Initialize I2C with your confirmed pins
  Wire.begin(SDA_PIN, SCL_PIN);
  Wire.setClock(100000); // 100kHz for reliability
  delay(100);
  
  Serial.println("Testing SHT30 sensor initialization...\n");
  
  // Try to initialize sensor at address 0x44 (ADDR pin = GND)
  Serial.print("Trying address 0x44 (ADDR pin connected to GND)... ");
  if (sht30_44.begin(0x44)) {
    Serial.println("✅ SUCCESS!");
    sensor_44_working = true;
    
    // Test reading
    float temp = sht30_44.readTemperature();
    float hum = sht30_44.readHumidity();
    
    if (!isnan(temp) && !isnan(hum)) {
      Serial.print("  Initial reading: ");
      Serial.print(temp, 1);
      Serial.print("°C, ");
      Serial.print(hum, 1);
      Serial.println("%");
    }
  } else {
    Serial.println("❌ Failed");
  }
  
  // Try to initialize sensor at address 0x45 (ADDR pin = VCC)
  Serial.print("Trying address 0x45 (ADDR pin connected to VCC)... ");
  if (sht30_45.begin(0x45)) {
    Serial.println("✅ SUCCESS!");
    sensor_45_working = true;
    
    // Test reading
    float temp = sht30_45.readTemperature();
    float hum = sht30_45.readHumidity();
    
    if (!isnan(temp) && !isnan(hum)) {
      Serial.print("  Initial reading: ");
      Serial.print(temp, 1);
      Serial.print("°C, ");
      Serial.print(hum, 1);
      Serial.println("%");
    }
  } else {
    Serial.println("❌ Failed");
  }
  
  // Summary
  Serial.println("\n========================================");
  if (sensor_44_working || sensor_45_working) {
    Serial.println("🎯 SHT30 SENSOR FOUND AND WORKING!");
    
    if (sensor_44_working) {
      Serial.println("✅ Address 0x44 working (ADDR pin = GND)");
    }
    if (sensor_45_working) {
      Serial.println("✅ Address 0x45 working (ADDR pin = VCC)");
    }
    
    Serial.println("\nStarting continuous readings...");
  } else {
    Serial.println("❌ NO SHT30 SENSOR FOUND!");
    Serial.println("\nPossible issues:");
    Serial.println("- ADDR pin not connected properly");
    Serial.println("- Power supply issue (needs 3.3V)");
    Serial.println("- Sensor not responding");
    Serial.println("\nWill keep retrying...");
  }
  Serial.println("========================================\n");
}

void loop() {
  // Test working sensors every 2 seconds
  static unsigned long lastReading = 0;
  if (millis() - lastReading >= 2000) {
    lastReading = millis();
    
    bool anyWorking = false;
    
    // Test sensor at 0x44 if it was working
    if (sensor_44_working) {
      if (testSensor(sht30_44, "0x44")) {
        anyWorking = true;
      } else {
        Serial.println("⚠️  Sensor 0x44 stopped working - retrying initialization...");
        sensor_44_working = sht30_44.begin(0x44);
      }
    }
    
    // Test sensor at 0x45 if it was working
    if (sensor_45_working) {
      if (testSensor(sht30_45, "0x45")) {
        anyWorking = true;
      } else {
        Serial.println("⚠️  Sensor 0x45 stopped working - retrying initialization...");
        sensor_45_working = sht30_45.begin(0x45);
      }
    }
    
    // If no sensors are working, try to reinitialize
    if (!anyWorking) {
      Serial.println("🔄 No sensors working - trying to reinitialize...");
      
      if (!sensor_44_working) {
        sensor_44_working = sht30_44.begin(0x44);
        if (sensor_44_working) {
          Serial.println("✅ Reconnected to sensor at 0x44!");
        }
      }
      
      if (!sensor_45_working) {
        sensor_45_working = sht30_45.begin(0x45);
        if (sensor_45_working) {
          Serial.println("✅ Reconnected to sensor at 0x45!");
        }
      }
    }
    
    // Show statistics every 10 readings
    if ((successCount + errorCount) % 10 == 0 && (successCount + errorCount) > 0) {
      showStatistics();
    }
  }
}

bool testSensor(Adafruit_SHT31& sensor, const char* address) {
  float temperature = sensor.readTemperature();
  float humidity = sensor.readHumidity();
  
  if (!isnan(temperature) && !isnan(humidity)) {
    successCount++;
    
    Serial.print("📡 ");
    Serial.print(address);
    Serial.print(": 🌡️ ");
    Serial.print(temperature, 1);
    Serial.print("°C  💧 ");
    Serial.print(humidity, 1);
    Serial.print("%  ✅ ");
    Serial.print(successCount);
    Serial.print("  ❌ ");
    Serial.println(errorCount);
    
    // Validate readings
    if (temperature < -10 || temperature > 50) {
      Serial.println("  ⚠️  Temperature seems unusual for indoor use");
    }
    if (humidity < 10 || humidity > 90) {
      Serial.println("  ⚠️  Humidity seems unusual for indoor use");
    }
    
    return true;
  } else {
    errorCount++;
    Serial.print("❌ ");
    Serial.print(address);
    Serial.print(": Read failed  ✅ ");
    Serial.print(successCount);
    Serial.print("  ❌ ");
    Serial.println(errorCount);
    
    return false;
  }
}

void showStatistics() {
  uint32_t total = successCount + errorCount;
  float successRate = (float)successCount / total * 100.0;
  
  Serial.println("\n--- SENSOR STATISTICS ---");
  Serial.print("Total readings: ");
  Serial.println(total);
  Serial.print("Success rate: ");
  Serial.print(successRate, 1);
  Serial.println("%");
  
  if (successRate > 95) {
    Serial.println("✅ Sensor working excellently!");
  } else if (successRate > 80) {
    Serial.println("⚠️  Sensor working with minor issues");
  } else {
    Serial.println("❌ Sensor having significant problems");
  }
  
  Serial.println("-------------------------\n");
}

// Helper function to test sensor features
void testSensorFeatures() {
  if (sensor_44_working) {
    Serial.println("Testing sensor 0x44 features...");
    testHeater(sht30_44);
  }
  
  if (sensor_45_working) {
    Serial.println("Testing sensor 0x45 features...");
    testHeater(sht30_45);
  }
}

void testHeater(Adafruit_SHT31& sensor) {
  Serial.println("  Testing built-in heater...");
  
  // Read temperature before heater
  float tempBefore = sensor.readTemperature();
  
  // Enable heater for 3 seconds
  sensor.heater(true);
  delay(3000);
  
  // Read temperature with heater
  float tempWithHeater = sensor.readTemperature();
  
  // Disable heater
  sensor.heater(false);
  
  if (!isnan(tempBefore) && !isnan(tempWithHeater)) {
    Serial.print("    Before: ");
    Serial.print(tempBefore, 1);
    Serial.print("°C, With heater: ");
    Serial.print(tempWithHeater, 1);
    Serial.println("°C");
    
    if (tempWithHeater > tempBefore + 1.0) {
      Serial.println("    ✅ Heater working - temperature increased");
    } else {
      Serial.println("    ⚠️  Heater may not be working");
    }
  }
}

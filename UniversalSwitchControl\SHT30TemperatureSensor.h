#ifndef SHT30_TEMPERATURE_SENSOR_H
#define SHT30_TEMPERATURE_SENSOR_H

#include "DeviceConfig.h"

// Only compile this library for devices with temperature sensor
#ifdef COMPILE_TEMPERATURE_SENSOR

#include <Wire.h>
#include "Adafruit_SHT31.h"

class SHT30TemperatureSensor
{
private:
    // Adafruit SHT31 sensor object
    Adafruit_SHT31 _sht30;

    uint8_t _sdaPin;
    uint8_t _sclPin;
    bool _initialized;

    // Last readings
    float _lastTemperature;
    float _lastHumidity;
    unsigned long _lastReadTime;

    // Reading interval to avoid overwhelming the sensor
    static const unsigned long READ_INTERVAL = 2000; // 2 seconds
public:
    SHT30TemperatureSensor()
        : _sdaPin(TEMP_SENSOR_SDA_PIN), _sclPin(TEMP_SENSOR_SCL_PIN),
          _initialized(false), _lastTemperature(0.0), _lastHumidity(0.0), _lastReadTime(0)
    {
        Serial.print(DEVICE_TYPE);
        Serial.println(" SHT30 Temperature Sensor constructor (Adafruit Library)");
    }

    // Initialize the sensor
    bool begin()
    {
        Serial.print(DEVICE_TYPE);
        Serial.println(" Initializing SHT30 temperature sensor with Adafruit library...");

        // Initialize I2C with custom pins
        Wire.begin(_sdaPin, _sclPin);
        Wire.setClock(100000); // 100kHz for SHT30
        delay(100);

        // Try to initialize sensor at address 0x44 first
        if (_sht30.begin(0x44))
        {
            Serial.print(DEVICE_TYPE);
            Serial.println(" SHT30 found at address 0x44");
            _initialized = true;
        }
        // If not found at 0x44, try 0x45
        else if (_sht30.begin(0x45))
        {
            Serial.print(DEVICE_TYPE);
            Serial.println(" SHT30 found at address 0x45");
            _initialized = true;
        }
        else
        {
            Serial.print(DEVICE_TYPE);
            Serial.println(" ERROR: Could not find SHT30 sensor at 0x44 or 0x45");
            return false;
        }

        // Test initial reading
        float temp = _sht30.readTemperature();
        float hum = _sht30.readHumidity();

        if (!isnan(temp) && !isnan(hum))
        {
            Serial.print(DEVICE_TYPE);
            Serial.print(" SHT30 initial reading: ");
            Serial.print(temp, 1);
            Serial.print("°C, ");
            Serial.print(hum, 1);
            Serial.println("%");

            _lastTemperature = temp;
            _lastHumidity = hum;
            _lastReadTime = millis();
        }
        else
        {
            Serial.print(DEVICE_TYPE);
            Serial.println(" WARNING: Initial SHT30 reading failed");
        }

        Serial.print(DEVICE_TYPE);
        Serial.println(" SHT30 temperature sensor initialized successfully");
        Serial.print("SDA Pin: ");
        Serial.print(_sdaPin);
        Serial.print(", SCL Pin: ");
        Serial.println(_sclPin);

        return true;
    }

    // Read temperature and humidity
    bool readSensor()
    {
        if (!_initialized)
        {
            Serial.println("ERROR: SHT30 not initialized");
            return false;
        }

        // Check if enough time has passed since last reading
        unsigned long now = millis();
        if (now - _lastReadTime < READ_INTERVAL)
        {
            return true; // Use cached values
        }

        // Read temperature and humidity using Adafruit library
        float temp = _sht30.readTemperature();
        float hum = _sht30.readHumidity();

        // Check if readings are valid
        if (isnan(temp) || isnan(hum))
        {
            Serial.println("ERROR: SHT30 reading failed (NaN values)");
            return false;
        }

        // Validate reasonable ranges
        if (temp < -40.0 || temp > 85.0 || hum < 0.0 || hum > 100.0)
        {
            Serial.print("WARNING: SHT30 readings out of range - T:");
            Serial.print(temp, 1);
            Serial.print("°C, H:");
            Serial.print(hum, 1);
            Serial.println("%");
            return false;
        }

        // Update cached values
        _lastTemperature = temp;
        _lastHumidity = hum;
        _lastReadTime = now;

        return true;
    }

    // Get temperature in Celsius
    float getTemperature()
    {
        readSensor(); // Update if needed
        return _lastTemperature;
    }

    // Get temperature in Fahrenheit
    float getTemperatureFahrenheit()
    {
        return getTemperature() * 9.0 / 5.0 + 32.0;
    }

    // Get humidity percentage
    float getHumidity()
    {
        readSensor(); // Update if needed
        return _lastHumidity;
    }

    // Get both values at once
    bool getTemperatureAndHumidity(float &temperature, float &humidity)
    {
        if (readSensor())
        {
            temperature = _lastTemperature;
            humidity = _lastHumidity;
            return true;
        }
        return false;
    }

    // Check if sensor is responding
    bool isConnected()
    {
        if (!_initialized)
            return false;

        // Try to read temperature - if it returns a valid value, sensor is connected
        float temp = _sht30.readTemperature();
        return !isnan(temp);
    }

    // Get formatted sensor reading as string
    String getReadingString()
    {
        if (readSensor())
        {
            return String(_lastTemperature, 1) + "°C, " + String(_lastHumidity, 1) + "%";
        }
        return "Sensor Error";
    }

    // Reset the sensor
    bool reset()
    {
        if (!_initialized)
            return false;

        // Use Adafruit library reset function
        _sht30.reset();
        delay(50);

        Serial.print(DEVICE_TYPE);
        Serial.println(" SHT30 sensor reset");

        return true;
    }
};

#endif // COMPILE_TEMPERATURE_SENSOR

#endif // SHT30_TEMPERATURE_SENSOR_H

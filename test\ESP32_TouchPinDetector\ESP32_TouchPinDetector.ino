/*
 * ESP32 Touch Pin Detection Test
 *
 * This test scans all available ESP32 GPIO pins to find which ones
 * are connected to touch sensors on your cooler control device.
 *
 * The current configuration expects pins 12, 15, 21 but they don't work,
 * so this test will help identify the actual pins.
 *
 * Features:
 * - Scans all safe ESP32 GPIO pins
 * - Real-time touch detection with visual feedback
 * - Interactive commands for focused testing
 * - Pin state monitoring and change detection
 * - Debouncing to prevent false triggers
 *
 * Usage:
 * 1. Upload to your ESP32 cooler control device
 * 2. Open Serial Monitor at 115200 baud
 * 3. Touch each physical button and note which pins respond
 * 4. Update your DeviceConfig.h with the working pins
 *
 * Author: ESP32 Touch Detection System
 * Date: 2025-01-27
 */

#include <WiFi.h>

// Test Configuration
#define DEBOUNCE_DELAY 50
#define SCAN_INTERVAL 10
#define REPORT_INTERVAL 5000

// ESP32 GPIO pins to test (excluding dangerous/reserved pins)
// Avoiding: 0,1,3,6,7,8,9,10,11 (flash/boot), 20 (not available), 24,28,29,30,31 (not available)
const uint8_t TEST_PINS[] = {2, 4, 5, 12, 13, 14, 15, 16, 17, 18, 19, 21, 22, 23, 25, 26, 27, 32, 33, 34, 35, 36, 39};
const uint8_t TEST_PIN_COUNT = sizeof(TEST_PINS) / sizeof(TEST_PINS[0]);

// Current expected pins (that don't work)
const uint8_t EXPECTED_PINS[] = {12, 15, 21};
const uint8_t EXPECTED_PIN_COUNT = 3;

// Touch sensor state tracking
struct TouchSensorState
{
  uint8_t pin;
  bool lastState;
  bool currentState;
  unsigned long lastChangeTime;
  unsigned long pressCount;
  bool isActive;
};

TouchSensorState touchSensors[TEST_PIN_COUNT];
unsigned long lastReportTime = 0;
unsigned long testStartTime = 0;

void setup()
{
  Serial.begin(115200);
  delay(1000);

  Serial.println("========================================");
  Serial.println("   ESP32 TOUCH PIN DETECTION TEST");
  Serial.println("========================================");
  Serial.println("Device: ESP32 Cooler Control");
  Serial.print("Testing ");
  Serial.print(TEST_PIN_COUNT);
  Serial.println(" GPIO pins for touch sensor activity");
  Serial.println("========================================");

  // Initialize all test pins
  initializeTouchSensors();

  // Show current expected pins (that don't work)
  Serial.println("\nCurrent expected pins (NOT WORKING):");
  for (uint8_t i = 0; i < EXPECTED_PIN_COUNT; i++)
  {
    Serial.print("  Touch Sensor ");
    Serial.print(i + 1);
    Serial.print(": Pin ");
    Serial.println(EXPECTED_PINS[i]);
  }

  // Start test
  testStartTime = millis();
  lastReportTime = testStartTime;

  Serial.println("\n>>> TOUCH DETECTION STARTED <<<");
  Serial.println(">>> Touch each physical button on your device");
  Serial.println(">>> Watch for 'TOUCH DETECTED!' messages");
  Serial.println(">>> Type 'help' for commands");
  Serial.println("========================================\n");
}

void loop()
{
  unsigned long currentTime = millis();

  // Check for serial commands
  if (Serial.available())
  {
    handleSerialCommand();
  }

  // Scan all pins for touch activity
  scanAllPins(currentTime);

  // Report status periodically
  if (currentTime - lastReportTime >= REPORT_INTERVAL)
  {
    reportStatus(currentTime);
    lastReportTime = currentTime;
  }

  delay(SCAN_INTERVAL);
}

void initializeTouchSensors()
{
  Serial.println("Initializing GPIO pins for touch detection...");

  for (uint8_t i = 0; i < TEST_PIN_COUNT; i++)
  {
    uint8_t pin = TEST_PINS[i];

    touchSensors[i].pin = pin;
    touchSensors[i].lastState = false;
    touchSensors[i].currentState = false;
    touchSensors[i].lastChangeTime = 0;
    touchSensors[i].pressCount = 0;
    touchSensors[i].isActive = true;

    // Initialize as INPUT_PULLUP for digital touch sensing
    pinMode(pin, INPUT_PULLUP);

    Serial.print("  Pin ");
    Serial.print(pin);
    if (isExpectedPin(pin))
    {
      Serial.print(" [EXPECTED - NOT WORKING]");
    }
    Serial.println(" initialized");
  }

  Serial.print("Total pins monitoring: ");
  Serial.println(TEST_PIN_COUNT);
}

bool isExpectedPin(uint8_t pin)
{
  for (uint8_t i = 0; i < EXPECTED_PIN_COUNT; i++)
  {
    if (EXPECTED_PINS[i] == pin)
    {
      return true;
    }
  }
  return false;
}

void scanAllPins(unsigned long currentTime)
{
  for (uint8_t i = 0; i < TEST_PIN_COUNT; i++)
  {
    TouchSensorState *sensor = &touchSensors[i];

    if (!sensor->isActive)
      continue;

    // Read current pin state (active LOW for touch sensors)
    bool currentState = digitalRead(sensor->pin) == LOW;

    // Check for state change with debouncing
    if (currentState != sensor->lastState)
    {
      if (currentTime - sensor->lastChangeTime >= DEBOUNCE_DELAY)
      {
        sensor->lastState = sensor->currentState;
        sensor->currentState = currentState;
        sensor->lastChangeTime = currentTime;

        // Report touch events
        if (currentState && !sensor->lastState)
        {
          // Touch pressed
          sensor->pressCount++;
          Serial.print("🔥 TOUCH DETECTED! Pin ");
          Serial.print(sensor->pin);
          Serial.print(" PRESSED (Count: ");
          Serial.print(sensor->pressCount);
          Serial.print(")");

          if (isExpectedPin(sensor->pin))
          {
            Serial.print(" [EXPECTED PIN - BUT WASN'T WORKING!]");
          }
          else
          {
            Serial.print(" [NEW DISCOVERY!]");
          }
          Serial.println();
        }
        else if (!currentState && sensor->lastState)
        {
          // Touch released
          Serial.print("   Pin ");
          Serial.print(sensor->pin);
          Serial.println(" RELEASED");
        }
      }
    }
  }
}

void handleSerialCommand()
{
  String command = Serial.readStringUntil('\n');
  command.trim();
  command.toLowerCase();

  if (command == "help" || command == "h")
  {
    printHelp();
  }
  else if (command == "status" || command == "s")
  {
    reportStatus(millis());
  }
  else if (command == "reset" || command == "r")
  {
    resetCounters();
  }
  else if (command == "pins" || command == "p")
  {
    showAllPinStates();
  }
  else if (command == "active" || command == "a")
  {
    showActivePins();
  }
  else if (command.startsWith("test "))
  {
    int pin = command.substring(5).toInt();
    testSpecificPin(pin);
  }
  else if (command == "scan")
  {
    performFullScan();
  }
  else if (command != "")
  {
    Serial.println("Unknown command. Type 'help' for available commands.");
  }
}

void printHelp()
{
  Serial.println("\n========== ESP32 TOUCH PIN COMMANDS ==========");
  Serial.println("help (h)      - Show this help message");
  Serial.println("status (s)    - Show detection status and results");
  Serial.println("reset (r)     - Reset all touch counters");
  Serial.println("pins (p)      - Show current state of all pins");
  Serial.println("active (a)    - Show only pins with detected activity");
  Serial.println("test <pin>    - Test specific pin for 10 seconds");
  Serial.println("scan          - Perform intensive scan of all pins");
  Serial.println("==============================================\n");
}

void resetCounters()
{
  for (uint8_t i = 0; i < TEST_PIN_COUNT; i++)
  {
    touchSensors[i].pressCount = 0;
  }
  testStartTime = millis();
  Serial.println(">>> All touch counters reset!");
}

void showAllPinStates()
{
  Serial.println("\n========== ALL PIN STATES ==========");
  for (uint8_t i = 0; i < TEST_PIN_COUNT; i++)
  {
    TouchSensorState *sensor = &touchSensors[i];
    bool currentReading = digitalRead(sensor->pin) == LOW;

    Serial.print("Pin ");
    Serial.print(sensor->pin);
    Serial.print(": ");
    Serial.print(currentReading ? "LOW (touched)" : "HIGH (normal)");
    Serial.print(" | Presses: ");
    Serial.print(sensor->pressCount);

    if (isExpectedPin(sensor->pin))
    {
      Serial.print(" [EXPECTED]");
    }
    if (sensor->pressCount > 0)
    {
      Serial.print(" ⭐ ACTIVE");
    }
    Serial.println();
  }
  Serial.println("====================================\n");
}

void showActivePins()
{
  Serial.println("\n========== ACTIVE PINS (WITH TOUCH ACTIVITY) ==========");
  bool foundActive = false;

  for (uint8_t i = 0; i < TEST_PIN_COUNT; i++)
  {
    TouchSensorState *sensor = &touchSensors[i];
    if (sensor->pressCount > 0)
    {
      Serial.print("🎯 Pin ");
      Serial.print(sensor->pin);
      Serial.print(": ");
      Serial.print(sensor->pressCount);
      Serial.print(" presses");

      if (isExpectedPin(sensor->pin))
      {
        Serial.print(" [WAS EXPECTED]");
      }
      else
      {
        Serial.print(" [NEW DISCOVERY]");
      }
      Serial.println();
      foundActive = true;
    }
  }

  if (!foundActive)
  {
    Serial.println("No pins with touch activity detected yet.");
    Serial.println("Try touching each physical button on your device.");
  }
  Serial.println("===================================================\n");
}

void reportStatus(unsigned long currentTime)
{
  unsigned long testDuration = (currentTime - testStartTime) / 1000;

  Serial.println("\n========== STATUS REPORT ==========");
  Serial.print("Test Duration: ");
  Serial.print(testDuration);
  Serial.println(" seconds");

  // Count active pins
  uint8_t activePinCount = 0;
  for (uint8_t i = 0; i < TEST_PIN_COUNT; i++)
  {
    if (touchSensors[i].pressCount > 0)
    {
      activePinCount++;
    }
  }

  Serial.print("Active pins found: ");
  Serial.print(activePinCount);
  Serial.print(" out of ");
  Serial.print(TEST_PIN_COUNT);
  Serial.println(" tested");

  if (activePinCount > 0)
  {
    Serial.println("\n🎯 DISCOVERED TOUCH PINS:");
    showActivePins();

    Serial.println("💡 NEXT STEPS:");
    Serial.println("1. Note the pin numbers above");
    Serial.println("2. Update DeviceConfig.h TOUCH_PINS with these numbers");
    Serial.println("3. Test in your main application");
  }
  else
  {
    Serial.println("\n❌ No touch activity detected yet.");
    Serial.println("💡 Try touching each physical button on your device.");
  }

  Serial.println("===================================\n");
}

void testSpecificPin(uint8_t pin)
{
  Serial.print("\n>>> TESTING PIN ");
  Serial.print(pin);
  Serial.println(" FOR 10 SECONDS <<<");

  // Check if pin is in our test list
  bool validPin = false;
  for (uint8_t i = 0; i < TEST_PIN_COUNT; i++)
  {
    if (TEST_PINS[i] == pin)
    {
      validPin = true;
      break;
    }
  }

  if (!validPin)
  {
    Serial.println("⚠️ Warning: This pin is not in the safe test list!");
    Serial.println("Proceeding anyway...");
  }

  pinMode(pin, INPUT_PULLUP);
  delay(100);

  Serial.println("Touch the sensor connected to this pin repeatedly...");

  unsigned long startTime = millis();
  bool lastState = digitalRead(pin);
  int changeCount = 0;
  unsigned long lastChange = startTime;

  while (millis() - startTime < 10000)
  {
    bool currentState = digitalRead(pin);
    if (currentState != lastState)
    {
      changeCount++;
      unsigned long now = millis();
      Serial.print("Change ");
      Serial.print(changeCount);
      Serial.print(" (");
      Serial.print(now - lastChange);
      Serial.print("ms): ");
      Serial.println(currentState == LOW ? "HIGH -> LOW (TOUCHED)" : "LOW -> HIGH (RELEASED)");
      lastState = currentState;
      lastChange = now;
    }
    delay(5);
  }

  Serial.print("\n>>> TEST COMPLETE <<<");
  Serial.print("\nPin ");
  Serial.print(pin);
  Serial.print(" had ");
  Serial.print(changeCount);
  Serial.println(" state changes");

  if (changeCount >= 2)
  {
    Serial.println("🎯 SUCCESS: This pin appears to be connected to a touch sensor!");
  }
  else if (changeCount == 1)
  {
    Serial.println("⚠️ PARTIAL: Pin changed once - might be a touch sensor");
  }
  else
  {
    Serial.println("❌ NO ACTIVITY: This pin doesn't appear to have a touch sensor");
  }
  Serial.println();
}

void performFullScan()
{
  Serial.println("\n>>> PERFORMING FULL INTENSIVE SCAN <<<");
  Serial.println("This will test each pin individually for 3 seconds");
  Serial.println("Touch sensors when prompted...\n");

  for (uint8_t i = 0; i < TEST_PIN_COUNT; i++)
  {
    uint8_t pin = TEST_PINS[i];

    Serial.print("Testing Pin ");
    Serial.print(pin);
    if (isExpectedPin(pin))
    {
      Serial.print(" [EXPECTED]");
    }
    Serial.print("... ");

    pinMode(pin, INPUT_PULLUP);
    delay(50);

    // Quick test for 3 seconds
    unsigned long startTime = millis();
    bool lastState = digitalRead(pin);
    int changes = 0;

    while (millis() - startTime < 3000)
    {
      bool currentState = digitalRead(pin);
      if (currentState != lastState)
      {
        changes++;
        lastState = currentState;
      }
      delay(10);
    }

    if (changes >= 2)
    {
      Serial.println("🎯 ACTIVE TOUCH SENSOR DETECTED!");
    }
    else if (changes == 1)
    {
      Serial.println("⚠️ Some activity");
    }
    else
    {
      Serial.println("No activity");
    }
  }

  Serial.println("\n>>> FULL SCAN COMPLETE <<<");
  Serial.println("Check results above for active pins");
  Serial.println("Use 'active' command to see summary\n");
}

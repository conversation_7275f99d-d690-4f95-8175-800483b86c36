/*
 * Simple SHT30 Temperature Sensor Test
 *
 * This test checks if the SHT30 sensor works with the current pin configuration.
 *
 * Current DeviceConfig.h settings:
 * - SDA Pin: GPIO 25
 * - SCL Pin: GPIO 26
 * - I2C Address: 0x44 or 0x45
 *
 * Usage:
 * 1. Upload this code to your ESP32
 * 2. Open Serial Monitor at 115200 baud
 * 3. Watch for temperature and humidity readings
 * 4. If no readings, the sensor or pins might be wrong
 *
 * Author: SHT30 Simple Test
 * Date: 2025-01-27
 */

#include <Wire.h>

// SHT30 Configuration (from DeviceConfig.h) - VERIFIED WORKING PINS
#define SDA_PIN 21
#define SCL_PIN 22
#define SHT30_ADDRESS_1 0x44
#define SHT30_ADDRESS_2 0x45

// SHT30 Commands
#define SHT30_SOFT_RESET 0x30A2
#define SHT30_MEASURE_HIGH 0x2C06

// Test settings
#define TEST_INTERVAL 2000 // Read every 2 seconds

uint8_t sensorAddress = 0;
bool sensorFound = false;
unsigned long lastReading = 0;
uint32_t successCount = 0;
uint32_t errorCount = 0;

void setup()
{
  Serial.begin(115200);
  delay(2000);

  Serial.println("========================================");
  Serial.println("      SHT30 SENSOR SIMPLE TEST");
  Serial.println("========================================");
  Serial.print("SDA Pin: GPIO");
  Serial.println(SDA_PIN);
  Serial.print("SCL Pin: GPIO");
  Serial.println(SCL_PIN);
  Serial.println("Testing addresses: 0x44, 0x45");
  Serial.println("========================================\n");

  // Initialize I2C
  Wire.begin(SDA_PIN, SCL_PIN);
  Wire.setClock(100000); // 100kHz

  Serial.println("Initializing I2C...");
  delay(100);

  // Find the sensor
  findSensor();

  if (sensorFound)
  {
    Serial.print("✅ SHT30 sensor found at address 0x");
    Serial.println(sensorAddress, HEX);
    Serial.println("Starting continuous readings...\n");
  }
  else
  {
    Serial.println("❌ SHT30 sensor NOT found!");
    Serial.println("Check connections and pin configuration.\n");
  }
}

void loop()
{
  unsigned long currentTime = millis();

  if (currentTime - lastReading >= TEST_INTERVAL)
  {
    lastReading = currentTime;

    if (sensorFound)
    {
      testSensorReading();
    }
    else
    {
      // Keep trying to find the sensor
      Serial.println("Retrying sensor detection...");
      findSensor();
      if (sensorFound)
      {
        Serial.print("✅ SHT30 sensor found at address 0x");
        Serial.println(sensorAddress, HEX);
      }
    }

    // Show statistics every 10 readings
    if ((successCount + errorCount) % 10 == 0 && (successCount + errorCount) > 0)
    {
      showStatistics();
    }
  }
}

void findSensor()
{
  Serial.println("Searching for SHT30 sensor...");

  // Try address 0x44
  if (testAddress(SHT30_ADDRESS_1))
  {
    sensorAddress = SHT30_ADDRESS_1;
    sensorFound = true;
    return;
  }

  // Try address 0x45
  if (testAddress(SHT30_ADDRESS_2))
  {
    sensorAddress = SHT30_ADDRESS_2;
    sensorFound = true;
    return;
  }

  sensorFound = false;
}

bool testAddress(uint8_t address)
{
  Serial.print("  Testing address 0x");
  Serial.print(address, HEX);
  Serial.print("... ");

  // Try to communicate
  Wire.beginTransmission(address);
  uint8_t error = Wire.endTransmission();

  if (error != 0)
  {
    Serial.println("No response");
    return false;
  }

  // Try to read sensor ID or perform soft reset
  Wire.beginTransmission(address);
  Wire.write(0x30); // Soft reset high byte
  Wire.write(0xA2); // Soft reset low byte
  error = Wire.endTransmission();

  if (error != 0)
  {
    Serial.println("Command failed");
    return false;
  }

  delay(50); // Wait for reset

  // Try a measurement
  Wire.beginTransmission(address);
  Wire.write(0x2C); // High repeatability measurement
  Wire.write(0x06);
  error = Wire.endTransmission();

  if (error != 0)
  {
    Serial.println("Measurement failed");
    return false;
  }

  delay(20); // Wait for measurement

  // Try to read result
  Wire.requestFrom(address, (uint8_t)6);
  if (Wire.available() == 6)
  {
    // Read and discard data for now
    for (int i = 0; i < 6; i++)
    {
      Wire.read();
    }
    Serial.println("SUCCESS!");
    return true;
  }

  Serial.println("No data");
  return false;
}

void testSensorReading()
{
  float temperature, humidity;

  if (readSHT30(temperature, humidity))
  {
    successCount++;

    Serial.print("🌡️  Temperature: ");
    Serial.print(temperature, 1);
    Serial.print("°C  |  💧 Humidity: ");
    Serial.print(humidity, 1);
    Serial.print("%  |  ✅ Success: ");
    Serial.print(successCount);
    Serial.print("  ❌ Errors: ");
    Serial.println(errorCount);

    // Check for reasonable values
    if (temperature < -10 || temperature > 50)
    {
      Serial.println("  ⚠️  Temperature seems unusual");
    }
    if (humidity < 10 || humidity > 90)
    {
      Serial.println("  ⚠️  Humidity seems unusual");
    }
  }
  else
  {
    errorCount++;
    Serial.print("❌ Failed to read sensor  |  ✅ Success: ");
    Serial.print(successCount);
    Serial.print("  ❌ Errors: ");
    Serial.println(errorCount);

    // Try to reconnect after several failures
    if (errorCount % 5 == 0)
    {
      Serial.println("  🔄 Multiple failures - retrying sensor detection...");
      sensorFound = false;
    }
  }
}

bool readSHT30(float &temperature, float &humidity)
{
  // Start measurement
  Wire.beginTransmission(sensorAddress);
  Wire.write(0x2C); // High repeatability measurement
  Wire.write(0x06);
  uint8_t error = Wire.endTransmission();

  if (error != 0)
  {
    Serial.println("  Error: Failed to start measurement");
    return false;
  }

  delay(20); // Wait for measurement to complete

  // Request 6 bytes of data
  Wire.requestFrom(sensorAddress, (uint8_t)6);

  if (Wire.available() != 6)
  {
    Serial.print("  Error: Expected 6 bytes, got ");
    Serial.println(Wire.available());
    return false;
  }

  // Read the data
  uint8_t data[6];
  for (int i = 0; i < 6; i++)
  {
    data[i] = Wire.read();
  }

  // Convert raw data to temperature and humidity
  uint16_t temp_raw = (data[0] << 8) | data[1];
  uint16_t hum_raw = (data[3] << 8) | data[4];

  temperature = -45.0 + 175.0 * temp_raw / 65535.0;
  humidity = 100.0 * hum_raw / 65535.0;

  // Basic validation
  if (temperature < -40.0 || temperature > 85.0)
  {
    Serial.println("  Error: Temperature out of range");
    return false;
  }

  if (humidity < 0.0 || humidity > 100.0)
  {
    Serial.println("  Error: Humidity out of range");
    return false;
  }

  return true;
}

void showStatistics()
{
  uint32_t total = successCount + errorCount;
  float successRate = (float)successCount / total * 100.0;

  Serial.println("\n--- STATISTICS ---");
  Serial.print("Total readings: ");
  Serial.println(total);
  Serial.print("Successful: ");
  Serial.print(successCount);
  Serial.print(" (");
  Serial.print(successRate, 1);
  Serial.println("%)");
  Serial.print("Failed: ");
  Serial.print(errorCount);
  Serial.print(" (");
  Serial.print(100.0 - successRate, 1);
  Serial.println("%)");

  if (successRate > 90)
  {
    Serial.println("✅ Sensor working excellently!");
  }
  else if (successRate > 70)
  {
    Serial.println("⚠️  Sensor working but with some issues");
  }
  else
  {
    Serial.println("❌ Sensor having significant problems");
  }
  Serial.println("------------------\n");
}

/*
 * ESP32 SHT30 Temperature Sensor Pin Scanner
 * 
 * This scanner finds the correct SDA and SCL pins for your SHT30 sensor
 * by testing all safe ESP32 GPIO pin combinations.
 * 
 * The current DeviceConfig.h has:
 * - SDA Pin: 25
 * - SCL Pin: 26
 * 
 * But these might not be correct. This scanner will find the actual pins.
 * 
 * Usage:
 * 1. Connect your SHT30 sensor to your ESP32 (any SDA/SCL pins)
 * 2. Upload this code
 * 3. Open Serial Monitor at 115200 baud
 * 4. Wait for scan results
 * 5. Update DeviceConfig.h with the working pins
 * 
 * Author: ESP32 SHT30 Scanner
 * Date: 2025-01-27
 */

#include <Wire.h>

// SHT30 I2C addresses
#define SHT30_ADDR_1 0x44
#define SHT30_ADDR_2 0x45

// SHT30 commands
#define SHT30_SOFT_RESET 0x30A2
#define SHT30_MEASURE_HIGH 0x2C06

// ESP32 pins safe for I2C (avoiding flash, boot, and input-only pins)
const uint8_t I2C_PINS[] = {4, 5, 13, 14, 15, 16, 17, 18, 19, 21, 22, 23, 25, 26, 27, 32, 33};
const uint8_t NUM_PINS = sizeof(I2C_PINS) / sizeof(I2C_PINS[0]);

// Current expected pins from DeviceConfig.h
const uint8_t EXPECTED_SDA = 25;
const uint8_t EXPECTED_SCL = 26;

struct SHT30Result {
  uint8_t sda_pin;
  uint8_t scl_pin;
  uint8_t address;
  float temperature;
  float humidity;
  bool valid;
};

SHT30Result workingConfigs[5];
uint8_t workingCount = 0;

void setup() {
  Serial.begin(115200);
  delay(2000);
  
  Serial.println("========================================");
  Serial.println("   ESP32 SHT30 SENSOR PIN SCANNER");
  Serial.println("========================================");
  Serial.println("Scanning for SHT30 temperature sensor...");
  Serial.print("Testing ");
  Serial.print(NUM_PINS * (NUM_PINS - 1));
  Serial.println(" pin combinations");
  Serial.println("========================================");
  
  Serial.println("\nCurrent DeviceConfig.h settings:");
  Serial.print("- Expected SDA: GPIO");
  Serial.println(EXPECTED_SDA);
  Serial.print("- Expected SCL: GPIO");
  Serial.println(EXPECTED_SCL);
  Serial.println();
  
  Serial.println("Starting scan...\n");
  
  // Test all pin combinations
  scanForSHT30();
  
  // Show results
  printResults();
}

void loop() {
  // Continuously test working configurations
  if (workingCount > 0) {
    Serial.println("\n--- Live Testing ---");
    
    for (uint8_t i = 0; i < workingCount; i++) {
      SHT30Result& config = workingConfigs[i];
      
      Serial.print("SDA=");
      Serial.print(config.sda_pin);
      Serial.print(", SCL=");
      Serial.print(config.scl_pin);
      Serial.print(" (0x");
      Serial.print(config.address, HEX);
      Serial.print("): ");
      
      if (testSHT30(config.sda_pin, config.scl_pin, config.address)) {
        float temp, hum;
        if (readSHT30(config.address, temp, hum)) {
          Serial.print("T=");
          Serial.print(temp, 1);
          Serial.print("°C, H=");
          Serial.print(hum, 1);
          Serial.println("%");
        } else {
          Serial.println("Read failed");
        }
      } else {
        Serial.println("Connection lost");
      }
    }
    
    delay(3000);
  } else {
    Serial.println("No working configurations. Check connections and restart.");
    delay(10000);
  }
}

void scanForSHT30() {
  uint16_t totalTests = NUM_PINS * (NUM_PINS - 1) * 2; // 2 addresses
  uint16_t currentTest = 0;
  
  for (uint8_t sda_idx = 0; sda_idx < NUM_PINS; sda_idx++) {
    for (uint8_t scl_idx = 0; scl_idx < NUM_PINS; scl_idx++) {
      if (sda_idx == scl_idx) continue; // Skip same pin
      
      uint8_t sda_pin = I2C_PINS[sda_idx];
      uint8_t scl_pin = I2C_PINS[scl_idx];
      
      // Show progress
      currentTest += 2;
      if (currentTest % 50 == 0) {
        Serial.print("Progress: ");
        Serial.print((currentTest * 100) / totalTests);
        Serial.println("%");
      }
      
      // Test both addresses
      testPinCombination(sda_pin, scl_pin, SHT30_ADDR_1);
      testPinCombination(sda_pin, scl_pin, SHT30_ADDR_2);
    }
  }
}

void testPinCombination(uint8_t sda_pin, uint8_t scl_pin, uint8_t address) {
  if (workingCount >= 5) return; // Limit results
  
  if (testSHT30(sda_pin, scl_pin, address)) {
    float temp, hum;
    if (readSHT30(address, temp, hum)) {
      // Valid reading - record it
      SHT30Result& config = workingConfigs[workingCount];
      config.sda_pin = sda_pin;
      config.scl_pin = scl_pin;
      config.address = address;
      config.temperature = temp;
      config.humidity = hum;
      config.valid = true;
      
      Serial.print("🎯 FOUND: SDA=GPIO");
      Serial.print(sda_pin);
      Serial.print(", SCL=GPIO");
      Serial.print(scl_pin);
      Serial.print(", Addr=0x");
      Serial.print(address, HEX);
      Serial.print(" - T=");
      Serial.print(temp, 1);
      Serial.print("°C, H=");
      Serial.print(hum, 1);
      Serial.print("%");
      
      // Check if this matches expected pins
      if (sda_pin == EXPECTED_SDA && scl_pin == EXPECTED_SCL) {
        Serial.print(" ✅ MATCHES EXPECTED!");
      } else {
        Serial.print(" ⚠️ DIFFERENT FROM EXPECTED");
      }
      Serial.println();
      
      workingCount++;
    }
  }
}

bool testSHT30(uint8_t sda_pin, uint8_t scl_pin, uint8_t address) {
  // End previous I2C session
  Wire.end();
  delay(10);
  
  // Start I2C with new pins
  Wire.begin(sda_pin, scl_pin);
  Wire.setClock(100000); // 100kHz
  delay(50);
  
  // Try to communicate with sensor
  Wire.beginTransmission(address);
  uint8_t error = Wire.endTransmission();
  
  if (error != 0) {
    return false;
  }
  
  // Try soft reset
  Wire.beginTransmission(address);
  Wire.write(0x30);
  Wire.write(0xA2);
  error = Wire.endTransmission();
  
  if (error != 0) {
    return false;
  }
  
  delay(50);
  
  // Try to start measurement
  Wire.beginTransmission(address);
  Wire.write(0x2C);
  Wire.write(0x06);
  error = Wire.endTransmission();
  
  return (error == 0);
}

bool readSHT30(uint8_t address, float& temperature, float& humidity) {
  // Start measurement
  Wire.beginTransmission(address);
  Wire.write(0x2C); // High repeatability
  Wire.write(0x06);
  uint8_t error = Wire.endTransmission();
  
  if (error != 0) return false;
  
  delay(20); // Wait for measurement
  
  // Read 6 bytes
  Wire.requestFrom(address, (uint8_t)6);
  
  if (Wire.available() != 6) {
    return false;
  }
  
  uint8_t data[6];
  for (int i = 0; i < 6; i++) {
    data[i] = Wire.read();
  }
  
  // Calculate temperature and humidity
  uint16_t temp_raw = (data[0] << 8) | data[1];
  uint16_t hum_raw = (data[3] << 8) | data[4];
  
  temperature = -45.0 + 175.0 * temp_raw / 65535.0;
  humidity = 100.0 * hum_raw / 65535.0;
  
  // Validate ranges
  return (temperature >= -40.0 && temperature <= 85.0 && 
          humidity >= 0.0 && humidity <= 100.0);
}

void printResults() {
  Serial.println("\n========================================");
  Serial.println("           SCAN RESULTS");
  Serial.println("========================================");
  
  if (workingCount == 0) {
    Serial.println("❌ NO SHT30 SENSOR FOUND!");
    Serial.println("\nPossible issues:");
    Serial.println("- SHT30 not connected");
    Serial.println("- Wrong power (needs 3.3V)");
    Serial.println("- Faulty sensor");
    Serial.println("- Different I2C address");
    Serial.println("- Bad connections");
  } else {
    Serial.print("✅ Found ");
    Serial.print(workingCount);
    Serial.println(" working configuration(s):");
    Serial.println();
    
    for (uint8_t i = 0; i < workingCount; i++) {
      SHT30Result& config = workingConfigs[i];
      
      Serial.print("Configuration ");
      Serial.print(i + 1);
      Serial.println(":");
      Serial.print("  SDA Pin: GPIO");
      Serial.println(config.sda_pin);
      Serial.print("  SCL Pin: GPIO");
      Serial.println(config.scl_pin);
      Serial.print("  Address: 0x");
      Serial.println(config.address, HEX);
      Serial.print("  Temperature: ");
      Serial.print(config.temperature, 1);
      Serial.println("°C");
      Serial.print("  Humidity: ");
      Serial.print(config.humidity, 1);
      Serial.println("%");
      
      // Check if matches expected
      if (config.sda_pin == EXPECTED_SDA && config.scl_pin == EXPECTED_SCL) {
        Serial.println("  ✅ MATCHES DeviceConfig.h");
      } else {
        Serial.println("  ⚠️ DIFFERENT from DeviceConfig.h");
      }
      Serial.println();
    }
    
    Serial.println("🎯 RECOMMENDED ACTION:");
    SHT30Result& best = workingConfigs[0];
    
    if (best.sda_pin == EXPECTED_SDA && best.scl_pin == EXPECTED_SCL) {
      Serial.println("✅ Current DeviceConfig.h pins are CORRECT!");
      Serial.println("No changes needed.");
    } else {
      Serial.println("⚠️ Update DeviceConfig.h with these pins:");
      Serial.print("#define TEMP_SENSOR_SDA_PIN ");
      Serial.println(best.sda_pin);
      Serial.print("#define TEMP_SENSOR_SCL_PIN ");
      Serial.println(best.scl_pin);
    }
  }
  
  Serial.println("========================================\n");
}

# GPIO Test Programs

This folder contains test programs to help you identify and verify GPIO pin functionality on your ESP8266/ESP32 devices.

## GPIO_Test.ino

A comprehensive GPIO pin testing program that systematically powers each GPIO pin one by one and prints detailed information about which pin is currently active.

### Features

- **Platform Detection**: Automatically detects ESP8266 vs ESP32 and uses appropriate pin ranges
- **Device Configuration Aware**: Reads your DeviceConfig.h and identifies device-specific pins
- **Pin Identification**: Shows what each pin is used for (relay, RGB LED, touch sensor, etc.)
- **Safe Pin Selection**: Only tests safe GPIO pins, avoiding flash memory and other restricted pins
- **Continuous Cycling**: Continuously cycles through all pins for easy identification

### How to Use

1. **Upload the Program**:
   - Open `GPIO_Test.ino` in Arduino IDE
   - Select your board (ESP8266 or ESP32)
   - Upload to your device

2. **Open Serial Monitor**:
   - Set baud rate to 115200
   - Watch the output as each pin is activated

3. **Verify Pins Physically**:
   - Use a multimeter to measure voltage on pins (should be 3.3V when active)
   - Or connect an LED with appropriate resistor to see visual indication
   - Each pin stays HIGH for 2 seconds before moving to the next

### Example Output

```
========================================
GPIO Pin Test Program
========================================
Platform: ESP32
Device Model: CoolerControl
Total pins to test: 20
========================================
Device Configuration:
- Uses Direct Pin Control
- Relay Count: 3
- Relay Pins: GPIO5, GPIO13, GPIO14
- RGB Red Pins: GPIO27, GPIO18, GPIO2
- RGB Green Pins: GPIO25, GPIO19, GPIO16
- RGB Blue Pins: GPIO26, GPIO17, GPIO23
- Touch Pins: GPIO12, GPIO15, GPIO21
- Temperature Sensor SDA: GPIO25
- Temperature Sensor SCL: GPIO26
- RF Receiver: GPIO27
- Buzzer: GPIO14
========================================
Starting GPIO test in 3 seconds...
Each pin will be HIGH for 2 seconds
Use a multimeter or LED to verify pins
========================================
Test starting now...

Pin GPIO0 -> ON (1/20) - General purpose GPIO
Pin GPIO0 -> OFF
Pin GPIO2 -> ON (2/20) - Switch 3 BLUE LED pin
Pin GPIO2 -> OFF
Pin GPIO4 -> ON (3/20) - General purpose GPIO
Pin GPIO4 -> OFF
Pin GPIO5 -> ON (4/20) - Relay 1 control pin
Pin GPIO5 -> OFF
...
```

### Pin Safety

The program only tests safe GPIO pins:

**ESP32 Safe Pins**: 0, 2, 4, 5, 12, 13, 14, 15, 16, 17, 18, 19, 21, 22, 23, 25, 26, 27, 32, 33

**ESP8266 Safe Pins**: 0, 1, 2, 3, 4, 5, 12, 13, 14, 15, 16

Avoided pins:
- ESP32: GPIO 6-11 (flash memory), GPIO 1, 3 (UART), GPIO 34-39 (input only)
- ESP8266: GPIO 6-11 (flash memory), GPIO 9-10 (flash)

### Customization

You can modify the test program:

- **Change test duration**: Modify `TEST_DELAY_MS` (default: 2000ms)
- **Change voltage level**: Modify `TEST_VOLTAGE` (default: HIGH)
- **Add/remove pins**: Modify the `TEST_PINS` array
- **Change serial baud**: Modify `SERIAL_BAUD_RATE` (default: 115200)

### Troubleshooting

1. **No serial output**: Check baud rate is set to 115200
2. **Device resets**: Some pins might be connected to reset circuits
3. **No voltage on pin**: Pin might be input-only or have external pull-down
4. **Unexpected behavior**: Pin might be used by internal systems

### Device-Specific Notes

- **Cooler Control (ESP32)**: Has additional pins for temperature sensor, RF receiver, and buzzer
- **Switch Models (ESP8266)**: Use shift registers, so only control pins will show direct voltage
- **Scenario Key**: No relay pins, only RGB and touch sensor pins

This test program helps you verify your hardware connections and identify any wiring issues before deploying your main application.

## SHT30_Pin_Scanner.ino

An intelligent I2C pin scanner that automatically detects the correct SDA and SCL pins for your SHT30 temperature sensor by testing all possible pin combinations.

### Features

- **Automatic Pin Detection**: Tests all possible I2C pin combinations systematically
- **Dual Address Support**: Tests both common SHT30 addresses (0x44 and 0x45)
- **Data Validation**: Verifies sensor responses with reasonable temperature/humidity ranges
- **Multiple Configuration Detection**: Finds all working pin combinations
- **Continuous Monitoring**: After detection, continuously reads from working configurations
- **Progress Tracking**: Shows scan progress during the detection process

### How to Use

1. **Connect Your SHT30 Sensor**:
   - Connect VCC to 3.3V
   - Connect GND to ground
   - Connect SDA and SCL to any GPIO pins (order doesn't matter initially)

2. **Upload the Program**:
   - Open `SHT30_Pin_Scanner.ino` in Arduino IDE
   - Upload to your device

3. **Monitor the Scan**:
   - Open Serial Monitor at 115200 baud
   - Wait for the scan to complete (may take several minutes)
   - Note the working SDA/SCL pin combinations

4. **Update Your Configuration**:
   - Use the detected pins in your `DeviceConfig.h`
   - Update `TEMP_SENSOR_SDA_PIN` and `TEMP_SENSOR_SCL_PIN`

### Example Output

```
========================================
SHT30 Temperature Sensor Pin Scanner
========================================
Platform: ESP32
Testing 380 pin combinations...
This may take several minutes...
========================================

Current Device Configuration:
- Configured SDA Pin: GPIO25
- Configured SCL Pin: GPIO26

Starting I2C pin scan...

Progress: 20/380 (5%)
Progress: 40/380 (10%)
...
*** FOUND WORKING CONFIGURATION *** SDA=GPIO21, SCL=GPIO22, Address=0x44 - T=23.5°C, H=45.2%
Progress: 200/380 (52%)
...
Progress: 380/380 (100%)

========================================
SCAN RESULTS
========================================
✅ Found 1 working configuration(s):

Configuration 1:
  SDA Pin: GPIO21
  SCL Pin: GPIO22
  I2C Address: 0x44
  Temperature: 23.5°C
  Humidity: 45.2%

RECOMMENDED CONFIGURATION:
Use SDA=GPIO21 and SCL=GPIO22 with address 0x44
========================================

--- Continuous Testing of Working Configurations ---
Testing SDA=21, SCL=22 (0x44): T=23.6°C, H=45.1%
```

### Technical Details

- **I2C Clock Speed**: 100kHz for maximum compatibility
- **Sensor Reset**: Performs soft reset before each test
- **Data Validation**: Checks for reasonable temperature (-40°C to +85°C) and humidity (0% to 100%) ranges
- **Timeout Protection**: Prevents hanging on failed communications
- **Memory Efficient**: Stores up to 10 working configurations

### Troubleshooting

1. **No configurations found**:
   - Check 3.3V power supply to sensor
   - Verify ground connections
   - Ensure sensor is genuine SHT30
   - Try different I2C pull-up resistors (4.7kΩ recommended)

2. **Multiple configurations found**:
   - This is normal - I2C can work on many pin combinations
   - Choose pins that don't conflict with other device functions
   - Prefer dedicated I2C pins if available

3. **Inconsistent readings**:
   - Check for loose connections
   - Verify power supply stability
   - Consider electromagnetic interference

### Integration

After finding the working pins, update your `DeviceConfig.h`:

```cpp
#define TEMP_SENSOR_SDA_PIN 21  // Use detected SDA pin
#define TEMP_SENSOR_SCL_PIN 22  // Use detected SCL pin
```

The scanner helps you identify the correct wiring without guessing or manual testing of each pin combination.

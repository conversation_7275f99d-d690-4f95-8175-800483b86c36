/*
 * SHT30 Address Test - Pins 21,22
 *
 * Since you know SDA=21 and SCL=22 are correct, this test focuses on
 * finding the right I2C address and checking for missing connections.
 *
 * SHT30 typically has these pins:
 * 1. VCC (3.3V)
 * 2. GND
 * 3. SDA (Pin 21) ✓
 * 4. SCL (Pin 22) ✓
 * 5. ADDR - Address select (might be missing!)
 * 6. RESET - Hardware reset (optional)
 * 7. ALERT - Alert pin (optional)
 *
 * Author: SHT30 Address Test
 * Date: 2025-01-27
 */

#include <Wire.h>
#include "Adafruit_SHT31.h"

#define SDA_PIN 21
#define SCL_PIN 22

// Create Adafruit SHT30 objects for both addresses
Adafruit_SHT31 sht30_44 = Adafruit_SHT31();
Adafruit_SHT31 sht30_45 = Adafruit_SHT31();

bool sensor_44_found = false;
bool sensor_45_found = false;

void setup()
{
  Serial.begin(115200);
  delay(2000);

  Serial.println("========================================");
  Serial.println("  SHT30 ADAFRUIT LIBRARY TEST");
  Serial.println("========================================");
  Serial.print("SDA: GPIO");
  Serial.println(SDA_PIN);
  Serial.print("SCL: GPIO");
  Serial.println(SCL_PIN);
  Serial.println("Using Adafruit SHT31 library");
  Serial.println("Testing I2C addresses 0x44 and 0x45");
  Serial.println("========================================\n");

  // Initialize I2C with custom pins
  Wire.begin(SDA_PIN, SCL_PIN);
  Wire.setClock(100000); // 100kHz for reliability
  delay(100);

  Serial.println("Step 1: General I2C Bus Scan");
  scanI2CBus();

  Serial.println("\nStep 2: Adafruit SHT30 Library Test");
  testAdafruitSHT30();

  Serial.println("\n========================================");
  Serial.println("DIAGNOSIS COMPLETE");
  Serial.println("========================================");
}

void loop()
{
  Serial.println("\n--- Live Test Every 5 Seconds ---");

  // Quick scan
  scanI2CBus();

  // Try to read SHT30 if found
  if (testSHT30Communication(0x44))
  {
    readAndDisplaySHT30(0x44);
  }
  else if (testSHT30Communication(0x45))
  {
    readAndDisplaySHT30(0x45);
  }
  else
  {
    Serial.println("❌ No SHT30 communication");
  }

  delay(5000);
}

void scanI2CBus()
{
  Serial.println("Scanning I2C bus for any devices...");

  uint8_t deviceCount = 0;
  for (uint8_t addr = 1; addr < 127; addr++)
  {
    Wire.beginTransmission(addr);
    uint8_t error = Wire.endTransmission();

    if (error == 0)
    {
      deviceCount++;
      Serial.print("  Device found at 0x");
      if (addr < 16)
        Serial.print("0");
      Serial.print(addr, HEX);

      if (addr == 0x44)
      {
        Serial.print(" ← SHT30 (ADDR pin = GND)");
      }
      else if (addr == 0x45)
      {
        Serial.print(" ← SHT30 (ADDR pin = VCC)");
      }
      Serial.println();
    }
  }

  if (deviceCount == 0)
  {
    Serial.println("  ❌ NO I2C devices found!");
    Serial.println("  Possible issues:");
    Serial.println("    - Power not connected (VCC/GND)");
    Serial.println("    - SDA/SCL pins wrong or not connected");
    Serial.println("    - Missing pull-up resistors");
    Serial.println("    - Sensor damaged");
  }
  else
  {
    Serial.print("  ✅ Found ");
    Serial.print(deviceCount);
    Serial.println(" device(s)");
  }
}

void testSHT30Addresses()
{
  Serial.println("Testing SHT30 specific addresses...");

  // Test 0x44 (ADDR = GND)
  Serial.print("  Address 0x44 (ADDR=GND): ");
  if (testSHT30Communication(0x44))
  {
    Serial.println("✅ WORKING!");
    readAndDisplaySHT30(0x44);
  }
  else
  {
    Serial.println("❌ No response");
  }

  // Test 0x45 (ADDR = VCC)
  Serial.print("  Address 0x45 (ADDR=VCC): ");
  if (testSHT30Communication(0x45))
  {
    Serial.println("✅ WORKING!");
    readAndDisplaySHT30(0x45);
  }
  else
  {
    Serial.println("❌ No response");
  }
}

bool testSHT30Communication(uint8_t address)
{
  // Try basic I2C communication
  Wire.beginTransmission(address);
  uint8_t error = Wire.endTransmission();

  if (error != 0)
  {
    return false;
  }

  // Try SHT30 soft reset command
  Wire.beginTransmission(address);
  Wire.write(0x30); // Soft reset high byte
  Wire.write(0xA2); // Soft reset low byte
  error = Wire.endTransmission();

  if (error != 0)
  {
    return false;
  }

  delay(50); // Wait for reset

  // Try measurement command
  Wire.beginTransmission(address);
  Wire.write(0x2C); // High repeatability measurement
  Wire.write(0x06);
  error = Wire.endTransmission();

  if (error != 0)
  {
    return false;
  }

  delay(20); // Wait for measurement

  // Try to read 6 bytes
  Wire.requestFrom(address, (uint8_t)6);
  return (Wire.available() == 6);
}

void readAndDisplaySHT30(uint8_t address)
{
  // Start new measurement
  Wire.beginTransmission(address);
  Wire.write(0x2C);
  Wire.write(0x06);
  uint8_t error = Wire.endTransmission();

  if (error != 0)
  {
    Serial.println("    ❌ Measurement command failed");
    return;
  }

  delay(20);

  // Read 6 bytes
  Wire.requestFrom(address, (uint8_t)6);
  if (Wire.available() != 6)
  {
    Serial.println("    ❌ Expected 6 bytes, got different amount");
    return;
  }

  uint8_t data[6];
  for (int i = 0; i < 6; i++)
  {
    data[i] = Wire.read();
  }

  // Convert to temperature and humidity
  uint16_t temp_raw = (data[0] << 8) | data[1];
  uint16_t hum_raw = (data[3] << 8) | data[4];

  float temperature = -45.0 + 175.0 * temp_raw / 65535.0;
  float humidity = 100.0 * hum_raw / 65535.0;

  Serial.print("    🌡️  Temperature: ");
  Serial.print(temperature, 2);
  Serial.print("°C  |  💧 Humidity: ");
  Serial.print(humidity, 2);
  Serial.println("%");

  // Validate readings
  if (temperature >= -40 && temperature <= 85 && humidity >= 0 && humidity <= 100)
  {
    Serial.println("    ✅ Readings look valid!");
    Serial.println("\n🎯 SUCCESS! Your SHT30 is working!");
    Serial.print("    Use address: 0x");
    Serial.println(address, HEX);
  }
  else
  {
    Serial.println("    ⚠️  Readings seem out of range");
  }
}

void testRawI2C()
{
  Serial.println("Testing raw I2C communication...");

  // Test both addresses with minimal commands
  uint8_t addresses[] = {0x44, 0x45};

  for (int i = 0; i < 2; i++)
  {
    uint8_t addr = addresses[i];
    Serial.print("  Raw test 0x");
    Serial.print(addr, HEX);
    Serial.print(": ");

    Wire.beginTransmission(addr);
    uint8_t error = Wire.endTransmission();

    switch (error)
    {
    case 0:
      Serial.println("✅ ACK received - device present");
      break;
    case 1:
      Serial.println("❌ Data too long");
      break;
    case 2:
      Serial.println("❌ NACK on address - device not found");
      break;
    case 3:
      Serial.println("❌ NACK on data");
      break;
    case 4:
      Serial.println("❌ Other error");
      break;
    case 5:
      Serial.println("❌ Timeout");
      break;
    default:
      Serial.print("❌ Unknown error: ");
      Serial.println(error);
      break;
    }
  }

  Serial.println("\nPossible missing connections:");
  Serial.println("  - ADDR pin: Controls I2C address (0x44 vs 0x45)");
  Serial.println("  - VCC: 3.3V power supply");
  Serial.println("  - GND: Ground connection");
  Serial.println("  - Pull-up resistors on SDA/SCL (usually built into PCB)");
}

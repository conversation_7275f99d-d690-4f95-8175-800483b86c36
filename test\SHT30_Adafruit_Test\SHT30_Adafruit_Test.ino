/*
 * SHT30 Test using Adafruit Library
 * 
 * This test uses the Adafruit SHT30 library which is more reliable
 * than raw I2C commands for testing the sensor.
 * 
 * Required Library:
 * Install "Adafruit SHT30" library from Arduino Library Manager
 * (Also installs Adafruit Sensor library automatically)
 * 
 * Current pins:
 * - SDA: GPIO 21
 * - SCL: GPIO 22
 * 
 * Usage:
 * 1. Install Adafruit SHT30 library
 * 2. Upload this code to ESP32
 * 3. Open Serial Monitor at 115200 baud
 * 4. Check if sensor readings appear
 * 
 * Author: SHT30 Adafruit Test
 * Date: 2025-01-27
 */

#include <Wire.h>
#include "Adafruit_SHT31.h"

// Pin configuration
#define SDA_PIN 21
#define SCL_PIN 22

// Create sensor object
Adafruit_SHT31 sht30 = Adafruit_SHT31();

// Test settings
unsigned long lastReading = 0;
const unsigned long READING_INTERVAL = 2000; // 2 seconds
uint32_t successCount = 0;
uint32_t errorCount = 0;
bool sensorInitialized = false;

void setup() {
  Serial.begin(115200);
  delay(2000);
  
  Serial.println("========================================");
  Serial.println("   SHT30 ADAFRUIT LIBRARY TEST");
  Serial.println("========================================");
  Serial.print("SDA Pin: GPIO");
  Serial.println(SDA_PIN);
  Serial.print("SCL Pin: GPIO");
  Serial.println(SCL_PIN);
  Serial.println("Using Adafruit SHT31 library");
  Serial.println("========================================\n");
  
  // Initialize I2C with custom pins
  Wire.begin(SDA_PIN, SCL_PIN);
  Wire.setClock(100000); // 100kHz
  
  Serial.println("Initializing SHT30 sensor...");
  
  // Try to initialize the sensor
  if (sht30.begin(0x44)) {
    Serial.println("✅ SHT30 sensor found at address 0x44");
    sensorInitialized = true;
  } else if (sht30.begin(0x45)) {
    Serial.println("✅ SHT30 sensor found at address 0x45");
    sensorInitialized = true;
  } else {
    Serial.println("❌ Could not find SHT30 sensor!");
    Serial.println("Check wiring and connections.");
    sensorInitialized = false;
  }
  
  if (sensorInitialized) {
    Serial.println("Starting continuous readings...\n");
    
    // Test heater functionality (optional)
    Serial.println("Testing sensor heater...");
    sht30.heater(true);
    delay(1000);
    sht30.heater(false);
    Serial.println("Heater test complete.\n");
  }
}

void loop() {
  unsigned long currentTime = millis();
  
  if (currentTime - lastReading >= READING_INTERVAL) {
    lastReading = currentTime;
    
    if (sensorInitialized) {
      testSensorReading();
    } else {
      // Try to reinitialize
      Serial.println("Retrying sensor initialization...");
      if (sht30.begin(0x44) || sht30.begin(0x45)) {
        Serial.println("✅ Sensor reconnected!");
        sensorInitialized = true;
      } else {
        Serial.println("❌ Still no sensor found");
      }
    }
    
    // Show statistics every 10 readings
    if ((successCount + errorCount) % 10 == 0 && (successCount + errorCount) > 0) {
      showStatistics();
    }
  }
}

void testSensorReading() {
  float temperature = sht30.readTemperature();
  float humidity = sht30.readHumidity();
  
  // Check if readings are valid
  if (!isnan(temperature) && !isnan(humidity)) {
    successCount++;
    
    Serial.print("🌡️  Temperature: ");
    Serial.print(temperature, 2);
    Serial.print("°C  |  💧 Humidity: ");
    Serial.print(humidity, 2);
    Serial.print("%  |  ✅ Success: ");
    Serial.print(successCount);
    Serial.print("  ❌ Errors: ");
    Serial.println(errorCount);
    
    // Check for reasonable values and warn if unusual
    if (temperature < -10 || temperature > 50) {
      Serial.println("  ⚠️  Temperature reading seems unusual for indoor use");
    }
    if (humidity < 10 || humidity > 90) {
      Serial.println("  ⚠️  Humidity reading seems unusual for indoor use");
    }
    
    // Check sensor status
    uint16_t status = sht30.readStatus();
    if (status != 0xFFFF) {
      Serial.print("  📊 Sensor Status: 0x");
      Serial.println(status, HEX);
      
      // Decode some status bits
      if (status & 0x8000) Serial.println("    - Alert pending");
      if (status & 0x0800) Serial.println("    - Heater enabled");
      if (status & 0x0400) Serial.println("    - RH tracking alert");
      if (status & 0x0200) Serial.println("    - T tracking alert");
      if (status & 0x0010) Serial.println("    - System reset detected");
      if (status & 0x0001) Serial.println("    - Command status: last command not processed");
    }
    
  } else {
    errorCount++;
    Serial.print("❌ Failed to read sensor");
    
    if (isnan(temperature)) Serial.print(" (temp=NaN)");
    if (isnan(humidity)) Serial.print(" (hum=NaN)");
    
    Serial.print("  |  ✅ Success: ");
    Serial.print(successCount);
    Serial.print("  ❌ Errors: ");
    Serial.println(errorCount);
    
    // Try to reset sensor after multiple failures
    if (errorCount % 5 == 0) {
      Serial.println("  🔄 Multiple failures - trying sensor reset...");
      sht30.reset();
      delay(100);
    }
  }
}

void showStatistics() {
  uint32_t total = successCount + errorCount;
  float successRate = (float)successCount / total * 100.0;
  
  Serial.println("\n--- SENSOR STATISTICS ---");
  Serial.print("Total readings: ");
  Serial.println(total);
  Serial.print("Successful: ");
  Serial.print(successCount);
  Serial.print(" (");
  Serial.print(successRate, 1);
  Serial.println("%)");
  Serial.print("Failed: ");
  Serial.print(errorCount);
  Serial.print(" (");
  Serial.print(100.0 - successRate, 1);
  Serial.println("%)");
  
  if (successRate > 95) {
    Serial.println("✅ Sensor working excellently!");
  } else if (successRate > 80) {
    Serial.println("⚠️  Sensor working well with minor issues");
  } else if (successRate > 50) {
    Serial.println("⚠️  Sensor working but with significant issues");
  } else {
    Serial.println("❌ Sensor having major problems");
  }
  
  // Additional diagnostics
  if (sensorInitialized) {
    Serial.println("\n--- SENSOR DIAGNOSTICS ---");
    
    // Try to read sensor status
    uint16_t status = sht30.readStatus();
    if (status != 0xFFFF) {
      Serial.print("Sensor status register: 0x");
      Serial.println(status, HEX);
    } else {
      Serial.println("Could not read sensor status");
    }
    
    // Try to read serial number
    Serial.print("Attempting to read sensor serial number... ");
    // Note: Serial number reading might not be available in all library versions
    Serial.println("(Feature may not be available)");
  }
  
  Serial.println("-------------------------\n");
}

// Additional helper functions for advanced testing
void performAdvancedTest() {
  if (!sensorInitialized) {
    Serial.println("Sensor not initialized for advanced test");
    return;
  }
  
  Serial.println("\n=== ADVANCED SENSOR TEST ===");
  
  // Test different measurement modes if available
  Serial.println("Testing high precision mode...");
  float temp1 = sht30.readTemperature();
  float hum1 = sht30.readHumidity();
  
  if (!isnan(temp1) && !isnan(hum1)) {
    Serial.print("High precision: T=");
    Serial.print(temp1, 3);
    Serial.print("°C, H=");
    Serial.print(hum1, 3);
    Serial.println("%");
  }
  
  // Test heater function
  Serial.println("Testing heater function...");
  Serial.println("Enabling heater for 3 seconds...");
  sht30.heater(true);
  delay(3000);
  
  float tempWithHeater = sht30.readTemperature();
  sht30.heater(false);
  
  if (!isnan(tempWithHeater)) {
    Serial.print("Temperature with heater: ");
    Serial.print(tempWithHeater, 2);
    Serial.println("°C");
    
    if (tempWithHeater > temp1 + 1.0) {
      Serial.println("✅ Heater working - temperature increased");
    } else {
      Serial.println("⚠️  Heater may not be working properly");
    }
  }
  
  Serial.println("=== ADVANCED TEST COMPLETE ===\n");
}

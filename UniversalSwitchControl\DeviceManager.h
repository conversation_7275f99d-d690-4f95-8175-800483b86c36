#ifndef DEVICE_MANAGER_H
#define DEVICE_MANAGER_H

#include <Arduino.h>
#include <EEPROM.h>
#include "DeviceConfig.h"

// Conditional includes based on device configuration
#if USE_SHIFT_REGISTERS
#include "ShiftRegisterManager.h"
#else
#include "DirectPinManager.h"
#endif

// Default RGB colors for basic devices (from config)
const bool DEFAULT_RGB_OFF[3] = {DEFAULT_RGB_OFF_R, DEFAULT_RGB_OFF_G, DEFAULT_RGB_OFF_B};
const bool DEFAULT_RGB_ON[3] = {DEFAULT_RGB_ON_R, DEFAULT_RGB_ON_G, DEFAULT_RGB_ON_B};

class DeviceManager
{
private:
    // Constants - no EEPROM needed
    const String _deviceID;
    const String _deviceType;
    const uint8_t _switchCount;

    // Variable - stored in EEPROM
    String _deviceName;
    bool _switchState[MAX_SWITCHES];
    bool _rgbStateOff[MAX_SWITCHES][3]; // RGB state when switch is OFF [r,g,b] - true=ON, false=OFF
    bool _rgbStateOn[MAX_SWITCHES][3];  // RGB state when switch is ON [r,g,b] - true=ON, false=OFF

    // Hardware control managers (conditional compilation)
#if USE_SHIFT_REGISTERS
    ShiftRegisterManager _shiftRegister;
#else
    DirectPinManager _directPinManager;
#endif

    // Callback function for state changes
    void (*_stateChangeCallback)();

    // Read string from EEPROM at specified address
    String readStringFromEEPROM(int startAddr, int maxLength)
    {
        char data[maxLength + 1];
        for (int i = 0; i < maxLength; i++)
        {
            byte b = EEPROM.read(startAddr + i);
            // Handle uninitialized EEPROM (0xFF) or null terminator
            if (b == 0xFF || b == 0)
            {
                data[i] = '\0';
                break;
            }
            // Only accept printable ASCII characters to prevent JSON issues
            if (b >= 32 && b <= 126)
            {
                data[i] = char(b);
            }
            else
            {
                data[i] = '\0';
                break;
            }
        }
        data[maxLength] = '\0';
        return String(data);
    }

    // Write string to EEPROM at specified address
    void writeStringToEEPROM(int startAddr, const String &strToWrite, int maxLength)
    {
        for (int i = 0; i < maxLength; i++)
        {
            if (i < strToWrite.length())
            {
                EEPROM.write(startAddr + i, strToWrite[i]);
            }
            else
            {
                EEPROM.write(startAddr + i, 0);
            }
        }
        EEPROM.commit();
    }

    // Generate a unique device ID from MAC address (static - no EEPROM needed)
    static String generateDeviceID()
    {
        // Use ESP8266's MAC address as a base for the device ID
        uint8_t mac[6];
        WiFi.macAddress(mac);

        char deviceID[13];
        sprintf(deviceID, "%02X%02X%02X%02X%02X%02X", mac[0], mac[1], mac[2], mac[3], mac[4], mac[5]);

        return String(deviceID);
    }

public:
    DeviceManager()
        :
#if USE_SHIFT_REGISTERS
          _shiftRegister(SR_DATA_PIN, SR_CLOCK_PIN, SR_LATCH_PIN),
#else
          _directPinManager(),
#endif
          _stateChangeCallback(nullptr),
          _deviceID(generateDeviceID()),
          _deviceType(DEVICE_TYPE),
          _switchCount(SWITCH_COUNT)
    {
        // Initialize switch states and RGB colors
        for (int i = 0; i < MAX_SWITCHES; i++)
        {
            _switchState[i] = false;

            // Set default RGB colors (Red=OFF, Green=ON)
            for (int j = 0; j < 3; j++)
            {
                _rgbStateOff[i][j] = DEFAULT_RGB_OFF[j];
                _rgbStateOn[i][j] = DEFAULT_RGB_ON[j];
            }
        }
    }

    // Initialize device properties
    void begin()
    {
        // Initialize hardware control managers
#if USE_SHIFT_REGISTERS
        _shiftRegister.begin();
#else
        _directPinManager.begin();
#endif

        // Set default device name based on device ID and type
        _deviceName = String(DEVICE_NAME_PREFIX) + _deviceID.substring(0, 6);

        // Check if device has been initialized before (only check device name)
        if (EEPROM.read(DEVICE_NAME_ADDR) == 0xFF)
        {
            // First time initialization - save device name and initialize states
            writeStringToEEPROM(DEVICE_NAME_ADDR, _deviceName, DEVICE_NAME_SIZE);

            // Initialize switch states and RGB states in EEPROM
            for (int i = 0; i < _switchCount; i++)
            {
                EEPROM.write(SWITCH_STATE_ADDR + i, 0); // All switches OFF

                // Initialize RGB states (OFF and ON colors)
                for (int j = 0; j < 3; j++)
                {
                    EEPROM.write(RGB_OFF_STATE_ADDR + (i * 3) + j, _rgbStateOff[i][j] ? 1 : 0);
                    EEPROM.write(RGB_ON_STATE_ADDR + (i * 3) + j, _rgbStateOn[i][j] ? 1 : 0);
                }
            }

            EEPROM.commit();

            Serial.print(DEVICE_TYPE);
            Serial.println(" device initialized for the first time");
        }
        else
        {
            // Load existing configuration (only variable data)
            loadFromEEPROM();
        }

        // Always display current configuration (constants + loaded data)
        Serial.print("Device ID: ");
        Serial.println(_deviceID);
        Serial.print("Device Name: ");
        Serial.println(_deviceName);
        Serial.print("Device Type: ");
        Serial.println(_deviceType);
        Serial.print("Switch Count: ");
        Serial.println(_switchCount);

        // Set initial states using appropriate control method
        for (int i = 0; i < _switchCount; i++)
        {
            // Set relay state
#if USE_SHIFT_REGISTERS
            _shiftRegister.setRelay(i, _switchState[i]);
#else
            _directPinManager.setRelay(i, _switchState[i]);
#endif

            // Set RGB based on current switch state
            updateRGBForSwitch(i);

            Serial.print("Initialized switch ");
            Serial.print(i + 1);
            Serial.print(" to ");
            Serial.print(_switchState[i] ? "ON" : "OFF");
            Serial.print(" with RGB OFF(");
            Serial.print(_rgbStateOff[i][0] ? "1" : "0");
            Serial.print(",");
            Serial.print(_rgbStateOff[i][1] ? "1" : "0");
            Serial.print(",");
            Serial.print(_rgbStateOff[i][2] ? "1" : "0");
            Serial.print(") ON(");
            Serial.print(_rgbStateOn[i][0] ? "1" : "0");
            Serial.print(",");
            Serial.print(_rgbStateOn[i][1] ? "1" : "0");
            Serial.print(",");
            Serial.print(_rgbStateOn[i][2] ? "1" : "0");
            Serial.println(")");
        }
    }

    // Load device properties from EEPROM (only variable data)
    void loadFromEEPROM()
    {
        // Only load device name (constants are set in constructor)
        _deviceName = readStringFromEEPROM(DEVICE_NAME_ADDR, DEVICE_NAME_SIZE);

        // If device name is empty or invalid, use default
        if (_deviceName.length() == 0)
        {
            _deviceName = String(DEVICE_NAME_PREFIX) + _deviceID.substring(0, 6);
            writeStringToEEPROM(DEVICE_NAME_ADDR, _deviceName, DEVICE_NAME_SIZE);
            EEPROM.commit();
        }

        // Load switch states and RGB states
        for (int i = 0; i < _switchCount; i++)
        {
            _switchState[i] = EEPROM.read(SWITCH_STATE_ADDR + i) == 1;

            // Load RGB states or use default if not found
            for (int j = 0; j < 3; j++)
            {
                uint8_t rgbOffValue = EEPROM.read(RGB_OFF_STATE_ADDR + (i * 3) + j);
                uint8_t rgbOnValue = EEPROM.read(RGB_ON_STATE_ADDR + (i * 3) + j);

                if (rgbOffValue == 0xFF)
                { // Invalid RGB value, use default
                    rgbOffValue = DEFAULT_RGB_OFF[j] ? 1 : 0;
                    EEPROM.write(RGB_OFF_STATE_ADDR + (i * 3) + j, rgbOffValue);
                    EEPROM.commit();
                }
                if (rgbOnValue == 0xFF)
                { // Invalid RGB value, use default
                    rgbOnValue = DEFAULT_RGB_ON[j] ? 1 : 0;
                    EEPROM.write(RGB_ON_STATE_ADDR + (i * 3) + j, rgbOnValue);
                    EEPROM.commit();
                }

                _rgbStateOff[i][j] = rgbOffValue == 1;
                _rgbStateOn[i][j] = rgbOnValue == 1;
            }
        }

        Serial.print(DEVICE_TYPE);
        Serial.println(" device configuration loaded from EEPROM");
        Serial.print("Device ID: ");
        Serial.println(_deviceID);
        Serial.print("Device Name: ");
        Serial.println(_deviceName);
        Serial.print("Device Type: ");
        Serial.println(_deviceType);
        Serial.print("Switch Count: ");
        Serial.println(_switchCount);
    }

    // Save device properties to EEPROM (only variable data)
    void saveToEEPROM()
    {
        // Only save device name (constants don't need to be saved)
        writeStringToEEPROM(DEVICE_NAME_ADDR, _deviceName, DEVICE_NAME_SIZE);

        // Save switch states and RGB states
        for (int i = 0; i < _switchCount; i++)
        {
            EEPROM.write(SWITCH_STATE_ADDR + i, _switchState[i] ? 1 : 0);
            // Save RGB states (both OFF and ON colors)
            for (int j = 0; j < 3; j++)
            {
                EEPROM.write(RGB_OFF_STATE_ADDR + (i * 3) + j, _rgbStateOff[i][j] ? 1 : 0);
                EEPROM.write(RGB_ON_STATE_ADDR + (i * 3) + j, _rgbStateOn[i][j] ? 1 : 0);
            }
        }

        EEPROM.commit();
        Serial.print(DEVICE_TYPE);
        Serial.println(" device configuration saved to EEPROM");
    }

    // Getters and setters
    String getDeviceID() const { return _deviceID; }

    String getDeviceName() const { return _deviceName; }
    void setDeviceName(const String &name)
    {
        _deviceName = name;
        saveToEEPROM();
    }

    String getDeviceType() const { return _deviceType; }

    uint8_t getSwitchCount() const { return _switchCount; }

    // Set callback function for state changes
    void setStateChangeCallback(void (*callback)())
    {
        _stateChangeCallback = callback;
    }

    bool getSwitchState(uint8_t switchIndex) const
    {
        if (switchIndex < _switchCount)
        {
            return _switchState[switchIndex];
        }
        return false;
    }

    void setSwitchState(uint8_t switchIndex, bool state)
    {
        if (switchIndex < _switchCount)
        {
            _switchState[switchIndex] = state;

            // Update EEPROM
            EEPROM.write(SWITCH_STATE_ADDR + switchIndex, state ? 1 : 0);
            EEPROM.commit();

            // Set the physical relay state
#if USE_SHIFT_REGISTERS
            _shiftRegister.setRelay(switchIndex, state);
#else
            _directPinManager.setRelay(switchIndex, state);
#endif

            // Update RGB based on new state
            updateRGBForSwitch(switchIndex);

            Serial.print(DEVICE_TYPE);
            Serial.print(" Switch ");
            Serial.print(switchIndex + 1);
            Serial.print(" set to ");
            Serial.println(state ? "ON" : "OFF");

            // Notify about state change
            if (_stateChangeCallback != nullptr)
            {
                _stateChangeCallback();
            }
        }
    }

    void toggleSwitch(uint8_t switchIndex)
    {
        if (switchIndex < _switchCount)
        {
            setSwitchState(switchIndex, !_switchState[switchIndex]);
        }
    }

    // RGB LED control methods - dual color system (OFF/ON states)
    void setRGBOff(uint8_t switchIndex, bool r, bool g, bool b)
    {
        if (switchIndex < _switchCount)
        {
            _rgbStateOff[switchIndex][0] = r;
            _rgbStateOff[switchIndex][1] = g;
            _rgbStateOff[switchIndex][2] = b;

            // Update EEPROM
            for (int j = 0; j < 3; j++)
            {
                EEPROM.write(RGB_OFF_STATE_ADDR + (switchIndex * 3) + j, _rgbStateOff[switchIndex][j] ? 1 : 0);
            }
            EEPROM.commit();

            // Update RGB if switch is currently OFF
            if (!_switchState[switchIndex])
            {
                updateRGBForSwitch(switchIndex);
            }

            Serial.print(DEVICE_TYPE);
            Serial.print(" RGB OFF color for Switch ");
            Serial.print(switchIndex + 1);
            Serial.print(" set to R:");
            Serial.print(r ? "1" : "0");
            Serial.print(" G:");
            Serial.print(g ? "1" : "0");
            Serial.print(" B:");
            Serial.println(b ? "1" : "0");

            // Notify about state change (RGB color change)
            if (_stateChangeCallback != nullptr)
            {
                _stateChangeCallback();
            }
        }
    }

    void setRGBOn(uint8_t switchIndex, bool r, bool g, bool b)
    {
        if (switchIndex < _switchCount)
        {
            _rgbStateOn[switchIndex][0] = r;
            _rgbStateOn[switchIndex][1] = g;
            _rgbStateOn[switchIndex][2] = b;

            // Update EEPROM
            for (int j = 0; j < 3; j++)
            {
                EEPROM.write(RGB_ON_STATE_ADDR + (switchIndex * 3) + j, _rgbStateOn[switchIndex][j] ? 1 : 0);
            }
            EEPROM.commit();

            // Update RGB if switch is currently ON
            if (_switchState[switchIndex])
            {
                updateRGBForSwitch(switchIndex);
            }

            Serial.print(DEVICE_TYPE);
            Serial.print(" RGB ON color for Switch ");
            Serial.print(switchIndex + 1);
            Serial.print(" set to R:");
            Serial.print(r ? "1" : "0");
            Serial.print(" G:");
            Serial.print(g ? "1" : "0");
            Serial.print(" B:");
            Serial.println(b ? "1" : "0");

            // Notify about state change (RGB color change)
            if (_stateChangeCallback != nullptr)
            {
                _stateChangeCallback();
            }
        }
    }

    void getRGBOff(uint8_t switchIndex, bool &r, bool &g, bool &b)
    {
        if (switchIndex < _switchCount)
        {
            r = _rgbStateOff[switchIndex][0];
            g = _rgbStateOff[switchIndex][1];
            b = _rgbStateOff[switchIndex][2];
        }
        else
        {
            r = g = b = false;
        }
    }

    void getRGBOn(uint8_t switchIndex, bool &r, bool &g, bool &b)
    {
        if (switchIndex < _switchCount)
        {
            r = _rgbStateOn[switchIndex][0];
            g = _rgbStateOn[switchIndex][1];
            b = _rgbStateOn[switchIndex][2];
        }
        else
        {
            r = g = b = false;
        }
    }

    // Helper method to update RGB based on current switch state
    void updateRGBForSwitch(uint8_t switchIndex)
    {
        if (switchIndex < _switchCount)
        {
            bool r, g, b;
            if (_switchState[switchIndex])
            {
                // Switch is ON - use ON colors
                getRGBOn(switchIndex, r, g, b);
            }
            else
            {
                // Switch is OFF - use OFF colors
                getRGBOff(switchIndex, r, g, b);
            }

            // Set RGB based on control method
#if USE_SHIFT_REGISTERS
            // Convert bool to uint8_t for shift register (255 for ON, 0 for OFF)
            _shiftRegister.setRGB(switchIndex, r ? 255 : 0, g ? 255 : 0, b ? 255 : 0);
#else
            // Direct pin control for RGB (supports full color range for ESP32)
            _directPinManager.setRGB(switchIndex, r ? 255 : 0, g ? 255 : 0, b ? 255 : 0);
#endif
        }
    }

    // Get hardware control managers for advanced control
#if USE_SHIFT_REGISTERS
    ShiftRegisterManager *getShiftRegister()
    {
        return &_shiftRegister;
    }
#else
    DirectPinManager *getDirectPinManager()
    {
        return &_directPinManager;
    }
#endif

private:
    // Private methods can be added here if needed
};

#endif // DEVICE_MANAGER_H

# ESP32 Touch Pin Detection Test

This test is specifically designed to find the actual touch sensor pins on your ESP32 cooler control device, since the current pins (12, 15, 21) don't seem to work.

## Quick Start

1. **Upload the code** to your ESP32 device
2. **Open Serial Monitor** at 115200 baud
3. **Touch each physical button** on your device
4. **Watch for "TOUCH DETECTED!" messages** showing which pins respond
5. **Note the working pin numbers** for updating your configuration

## What This Test Does

- **Scans 23 safe ESP32 GPIO pins** for touch sensor activity
- **Shows real-time detection** when you touch physical buttons
- **Identifies working pins** vs the expected pins (12, 15, 21) that don't work
- **Provides interactive commands** for focused testing

## Expected Output

When you touch a physical button, you should see:
```
🔥 TOUCH DETECTED! Pin 4 PRESSED (Count: 1) [NEW DISCOVERY!]
   Pin 4 RELEASED
```

## Interactive Commands

Type these in the Serial Monitor:

- **`help`** - Show all available commands
- **`status`** - Show current results and discovered pins
- **`active`** - Show only pins that have detected touch activity
- **`pins`** - Show current state of all monitored pins
- **`test 4`** - Test pin 4 specifically for 10 seconds
- **`scan`** - Run intensive test of all pins (takes ~1 minute)
- **`reset`** - Reset all touch counters

## Tested GPIO Pins

The test monitors these ESP32 pins (avoiding flash/boot pins):
```
2, 4, 5, 12, 13, 14, 15, 16, 17, 18, 19, 21, 22, 23, 25, 26, 27, 32, 33, 34, 35, 36, 39
```

## Troubleshooting

### No Touch Detection
1. **Verify physical connections** - Make sure touch sensors are properly wired
2. **Check power** - Ensure the device is powered correctly
3. **Try the scan command** - Type `scan` for intensive testing
4. **Test specific pins** - Use `test <pin>` for individual pin testing

### False Positives
- Some pins might show activity from electrical noise
- Look for consistent, repeatable touch events
- Use the `test <pin>` command to verify specific pins

### Expected Pins Still Don't Work
If pins 12, 15, 21 still don't respond, they may be:
- **Incorrectly wired** in the hardware
- **Used for other purposes** (relays, LEDs, etc.)
- **Damaged** or have hardware issues

## Next Steps

Once you identify the working pins:

1. **Update DeviceConfig.h**:
   ```cpp
   // Replace the current line:
   #define TOUCH_PINS {12, 15, 21}
   
   // With your discovered pins, for example:
   #define TOUCH_PINS {4, 5, 13}  // Use your actual working pins
   ```

2. **Test in main application** - Upload your main code and verify touch functionality

3. **Document the changes** - Note the correct pins for future reference

## Example Session

```
========================================
   ESP32 TOUCH PIN DETECTION TEST
========================================
Device: ESP32 Cooler Control
Testing 23 GPIO pins for touch sensor activity
========================================

>>> TOUCH DETECTION STARTED <<<
>>> Touch each physical button on your device
>>> Watch for 'TOUCH DETECTED!' messages

🔥 TOUCH DETECTED! Pin 4 PRESSED (Count: 1) [NEW DISCOVERY!]
   Pin 4 RELEASED
🔥 TOUCH DETECTED! Pin 5 PRESSED (Count: 1) [NEW DISCOVERY!]
   Pin 5 RELEASED
🔥 TOUCH DETECTED! Pin 13 PRESSED (Count: 1) [NEW DISCOVERY!]
   Pin 13 RELEASED

========== STATUS REPORT ==========
Active pins found: 3 out of 23 tested

🎯 DISCOVERED TOUCH PINS:
🎯 Pin 4: 1 presses [NEW DISCOVERY]
🎯 Pin 5: 1 presses [NEW DISCOVERY]
🎯 Pin 13: 1 presses [NEW DISCOVERY]

💡 NEXT STEPS:
1. Note the pin numbers above
2. Update DeviceConfig.h TOUCH_PINS with these numbers
3. Test in your main application
```

## Safety Notes

- The test avoids dangerous pins (flash, boot, power)
- All tested pins are safe for INPUT_PULLUP configuration
- If you see warnings about specific pins, they're still generally safe to test
- Stop testing if you notice any unusual device behavior

## Support

If you need help:
1. Check the Serial Monitor output for clear messages
2. Use the `help` command to see all available options
3. Try the `scan` command for comprehensive testing
4. Document which physical buttons correspond to which pins

This test should definitively identify your actual touch sensor pins!

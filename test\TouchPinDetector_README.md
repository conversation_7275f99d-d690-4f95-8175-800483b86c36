# Touch Pin Detection Test

This test code helps identify which GPIO pins are connected to touch sensors on your ESP32/ESP8266 device.

## Features

- **Automatic Pin Scanning**: Monitors all safe GPIO pins for touch activity
- **Real-time Detection**: Shows immediate feedback when touch sensors are pressed
- **Interactive Commands**: Control the test via Serial Monitor commands
- **Device Model Support**: Pre-configured for different switch device models
- **Debouncing**: Prevents false triggers from electrical noise
- **Safety Checks**: Avoids testing pins that could damage the device

## Setup Instructions

1. **Configure Device Model**: 
   - Open `TouchPinDetector.ino`
   - Uncomment the line for your device model (e.g., `#define DEVICE_MODEL_COOLER_CONTROL`)
   - Comment out other device model definitions

2. **Upload Code**:
   - Connect your device via USB
   - Select the correct board and port in Arduino IDE
   - Upload the code

3. **Open Serial Monitor**:
   - Set baud rate to **115200**
   - Ensure "Both NL & CR" is selected for line endings

## Usage

### Automatic Detection
- The test runs continuously and shows touch events in real-time
- Touch each sensor button on your device
- Watch for "TOUCH DETECTED!" messages showing which pins respond
- Expected pins are marked with ✓, unexpected pins with ⚠

### Interactive Commands
Type these commands in the Serial Monitor:

- `help` or `h` - Show available commands
- `status` or `s` - Show current status and touch counts
- `reset` or `r` - Reset all touch counters
- `test` or `t` - Run advanced pin test on expected pins
- `pins` or `p` - Show current state of all monitored pins
- `pin <number>` - Test a specific pin (e.g., `pin 12`)

### Expected Pin Configurations

| Device Model | Expected Touch Pins | Description |
|--------------|-------------------|-------------|
| 1-Switch | 4 | Single touch sensor |
| 2-Switch | 4, 5 | Two touch sensors |
| 3-Switch | 4, 5, 12 | Three touch sensors |
| 4-Switch | 4, 5, 12, 0 | Four touch sensors |
| Cooler Control | 12, 15, 21 | ESP32-based cooler control |
| Scenario Key | 4, 5, 12, 0 | Four scenario keys |

## Interpreting Results

### Normal Operation
```
>>> TOUCH DETECTED! Pin 12 PRESSED (Count: 1)
    ✓ This matches expected touch sensor pin!
>>> Pin 12 RELEASED
```

### Unexpected Pin Activity
```
>>> TOUCH DETECTED! Pin 2 PRESSED (Count: 1)
    ⚠ This is NOT an expected touch sensor pin!
```

### No Activity
If no touch events are detected:
1. Check physical connections to touch sensors
2. Verify the correct device model is selected
3. Use `pins` command to see current pin states
4. Try `test` command for advanced diagnostics

## Troubleshooting

### Pin Stuck LOW
```
⚠ Pin appears to be stuck LOW - possible hardware issue
```
- Check for short circuits
- Verify touch sensor wiring
- Test with multimeter

### Pin State Unstable
```
? Pin state is unstable - check connections
```
- Check for loose connections
- Verify proper pull-up resistors
- Check for electrical interference

### No Touch Detection
1. Verify device model selection matches your hardware
2. Check that touch sensors are properly connected
3. Use `pin <number>` command to test specific pins
4. Try touching sensors while watching Serial Monitor

## Safety Notes

- The test automatically avoids pins used for flash memory and UART
- Some pins may show warnings but are generally safe to test
- If unsure about a pin, consult your device's pinout diagram
- Stop the test if you notice any unusual behavior

## Device-Specific Notes

### ESP32 (Cooler Control)
- Uses digital touch sensors (not capacitive)
- More GPIO pins available for testing
- Pins 12, 15, 21 are expected for cooler control model

### ESP8266 (Switch Models)
- Limited GPIO pins available
- Pins 4, 5, 12 commonly used for touch sensors
- Pin 0 may be used on 4-switch models

## Next Steps

After identifying the correct touch pins:
1. Update your main project's `DeviceConfig.h` with the correct pin numbers
2. Test the touch functionality in your main application
3. Adjust debounce timing if needed
4. Consider adding additional touch sensors if pins are available

## Support

If you encounter issues:
1. Check the Serial Monitor output for error messages
2. Verify your device model selection
3. Test with known working touch sensors
4. Consult the device documentation for pin assignments
